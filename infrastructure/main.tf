terraform {
  required_version = ">= 1.4.6"

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.28.0"
    }

    azuread = {
      source  = "hashicorp/azuread"
      version = "3.3.0"

    }

    random = {
      source  = "hashicorp/random"
      version = "3.7.1"
    }

    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.4"
    }
    fabric = {
      source  = "microsoft/fabric"
      version = "~> 1.2"
    }
  }

  backend "azurerm" {
  }
}

data "azurerm_client_config" "current" {}

provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
}

# Configure the Azure Active Directory Provider
provider "azuread" {
  tenant_id = data.azurerm_client_config.current.tenant_id
}

# Get existing virtual network
data "azurerm_virtual_network" "vnet" {
  name                = local.vnet_name
  resource_group_name = local.vnet_rg
}

# Get existing keyvault
data "azurerm_key_vault" "keyvault" {
  name                = local.key_vault_name
  resource_group_name = local.key_vault_rg
}

data "azurerm_key_vault_secret" "spn-app-id-owner" {
  name         = "spn-app-id-owner"
  key_vault_id = data.azurerm_key_vault.keyvault.id
}

data "azurerm_key_vault_secret" "spn-secret-owner" {
  name         = "spn-secret-owner"
  key_vault_id = data.azurerm_key_vault.keyvault.id
}

resource "azurerm_resource_group" "aks-rg" {
  name     = local.cluster_rg
  location = local.location
}
