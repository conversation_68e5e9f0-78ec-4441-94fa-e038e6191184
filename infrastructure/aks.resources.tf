resource "azurerm_kubernetes_cluster" "aks_cluster" {
  name                              = local.aks_cluster_name
  location                          = local.location
  resource_group_name               = azurerm_resource_group.aks-rg.name
  dns_prefix                        = local.dns_prefix
  node_resource_group               = local.nodepool_rg
  sku_tier                          = local.aks_sku_tier
  private_dns_zone_id               = local.private_dns_zone_id
  disk_encryption_set_id            = azurerm_disk_encryption_set.aks-diskencryption.id
  azure_policy_enabled              = true
  private_cluster_enabled           = true
  oidc_issuer_enabled               = true
  role_based_access_control_enabled = true
  workload_identity_enabled         = true

  network_profile {
    network_plugin    = "azure"
    dns_service_ip    = "***********"
    service_cidr      = "***********/16"
    outbound_type     = "userDefinedRouting"
    load_balancer_sku = "standard"
  }

  default_node_pool {
    name                        = "general"
    vm_size                     = local.general_nodepool_vm_size
    node_count                  = local.general_nodepool_count
    zones                       = local.general_nodepool_zones
    vnet_subnet_id              = azurerm_subnet.aks_nodepool_subnet.id
    max_pods                    = 40
    temporary_name_for_rotation = "generaltemp"

    upgrade_settings { max_surge = "50%" }
  }

  service_principal {
    client_id     = data.azurerm_key_vault_secret.spn-app-id-owner.value
    client_secret = data.azurerm_key_vault_secret.spn-secret-owner.value
  }

  key_vault_secrets_provider {
    secret_rotation_enabled = true
  }

  upgrade_override {
    effective_until       = local.aks_upgrade_override_effective_until_date
    force_upgrade_enabled = false
  }

  depends_on = [
    azurerm_subnet_route_table_association.aks_rt,
    azurerm_disk_encryption_set.aks-diskencryption
  ]

  lifecycle {
    ignore_changes = [
      tags,
      upgrade_override
    ]
  }
}

resource "azurerm_kubernetes_cluster_node_pool" "general_cluster_node_pool" {
  count                 = local.environment == "dev" ? 0 : 1
  mode                  = "System"
  kubernetes_cluster_id = azurerm_kubernetes_cluster.aks_cluster.id
  name                  = "general"
  vm_size               = local.general_nodepool_vm_size
  node_count            = local.general_nodepool_count
  zones                 = local.general_nodepool_zones
  vnet_subnet_id        = azurerm_subnet.aks_nodepool_subnet.id
}
