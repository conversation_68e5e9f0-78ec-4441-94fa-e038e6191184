data "azurerm_resource_group" "sharedsvcs-rg" {
  name = local.sharedsvcs_rg
}

resource "azurerm_disk_encryption_set" "aks-diskencryption" {
  name                = "aks-diskencryption"
  resource_group_name = data.azurerm_resource_group.sharedsvcs-rg.name
  location            = data.azurerm_resource_group.sharedsvcs-rg.location
  key_vault_key_id    = azurerm_key_vault_key.diskencryption-key.id

  identity {
    type = "SystemAssigned"
  }

  lifecycle {
    ignore_changes = [
      tags
    ]
  }

}

resource "azurerm_key_vault_key" "diskencryption-key" {
  name         = "diskencryption"
  key_vault_id = data.azurerm_key_vault.keyvault.id
  key_type     = "RSA"
  key_size     = 2048

  key_opts = [
    "decrypt",
    "encrypt",
    "sign",
    "unwrapKey",
    "verify",
    "wrapKey",
  ]
}

resource "azurerm_role_assignment" "diskencryption-role" {
  scope                = data.azurerm_key_vault.keyvault.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_disk_encryption_set.aks-diskencryption.identity[0].principal_id
}
