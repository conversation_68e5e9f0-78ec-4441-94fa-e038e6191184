variable "systemname" {
  type = map(string)
  default = {
    dev   = "cloud-data"
    stage = "cloud-data"
    prod  = "cloud-data"
  }
}

variable "location" {
  type = map(string)
  default = {
    dev   = "swedencentral"
    stage = "swedencentral"
    prod  = "swedencentral"
  }
}

# prod subnet address spaces
# ***********/24    -- aks
# ***********/25    -- unassigned
# *************/26  -- unassigned
# *************/26  -- db
variable "aks_default_address_space" {
  type = map(string)
  default = {
    dev   = "***********/23"
    stage = "*********/24"
    prod  = "***********/24"
  }
}

variable "aks_address_space" {
  type = map(string)
  default = {
    dev   = "***********/23"
    stage = "**********/23"
    prod  = "***********/23"
  }
}

variable "general_nodepool_vm_size" {
  type = map(string)
  default = {
    dev   = "Standard_D4_v2"
    stage = "Standard_D4_v2"
    prod  = "Standard_D16as_v5"
  }
}

variable "general_nodepool_count" {
  type = map(number)
  default = {
    dev   = 6
    stage = 7
    prod  = 7
  }
}

variable "general_nodepool_zones" {
  type = map(list(string))
  default = {
    dev   = ["1", "2", "3"]
    stage = ["1", "2", "3"]
    prod  = ["1", "2", "3"]
  }
}

variable "private_dns_zone_id" {
  type = map(string)
  default = {
    dev   = "/subscriptions/82899754-6c6e-477c-bc26-92eb442b90f9/resourceGroups/global-itops-infrastructure-01/providers/Microsoft.Network/privateDnsZones/privatelink.swedencentral.azmk8s.io"
    stage = "/subscriptions/82899754-6c6e-477c-bc26-92eb442b90f9/resourceGroups/global-itops-infrastructure-01/providers/Microsoft.Network/privateDnsZones/privatelink.swedencentral.azmk8s.io"
    prod  = "/subscriptions/82899754-6c6e-477c-bc26-92eb442b90f9/resourceGroups/global-itops-infrastructure-01/providers/Microsoft.Network/privateDnsZones/privatelink.swedencentral.azmk8s.io"
  }
}

variable "aks_sku_tier" {
  type = map(string)
  default = {
    dev   = "Free"
    stage = "Free"
    prod  = "Free"
  }
}


variable "existing_sharedsvcs_name_rg" {
  type = map(string)
  default = {
    dev   = "dev-cloud-data-sharedsvcs-rg"
    stage = "stage-cloud-data-sharedsvcs-rg"
    prod  = "prod-cloud-data-sharedsvcs-rg"
  }
}

variable "slack_channel_email" {
  type        = map(string)
  description = "The email address for the Slack channel integration used for alert notifications."
  default = {
    dev   = "<EMAIL>"
    stage = "<EMAIL>"
    prod  = "<EMAIL>"
  }
  sensitive = true
}

variable "existing_sharedsvcs_name_sa" {
  type = map(string)
  default = {
    dev   = "devclouddatasa63"
    stage = "stageclouddatasa15"
    prod  = "prodclouddatasa30"
  }
}

variable "existing_key_vault_name" {
  type = map(string)
  default = {
    dev   = "dev-cloud-data-kv-63"
    stage = "stage-cloud-data-kv-15"
    prod  = "prod-cloud-data-kv-30"
  }
}

variable "existing_vnet_rg_name" {
  type = map(string)
  default = {
    dev   = "dev-cloud-data-vnet-rg"
    stage = "stage-cloud-data-vnet-rg"
    prod  = "prod-cloud-data-vnet-rg"
  }
}
variable "existing_vnet_name" {
  type = map(string)
  default = {
    dev   = "dev-cloud-data-vnet-01-63"
    stage = "stage-cloud-data-vnet-01-15"
    prod  = "prod-cloud-data-vnet-01-30"
  }
}

variable "federated_identities" {
  type = map(list(string))
  default = {
    dev   = ["argocd", "kafka", "monitoring", "ingress-nginx", "automate", "cert-manager", "dp-kyc"]
    stage = ["argocd", "kafka", "monitoring", "ingress-nginx", "automate", "cert-manager", "dp-kyc"]
    prod  = ["argocd", "kafka", "monitoring", "ingress-nginx", "automate", "cert-manager"]
  }
}

# https://learn.microsoft.com/en-us/azure/active-directory/workload-identities/workload-identity-federation-considerations#unsupported-regions-user-assigned-managed-identities
variable "identity_location" {
  type = map(string)
  default = {
    dev   = "westeurope"
    stage = "westeurope"
    prod  = "westeurope"
  }
}

variable "alert_rule_location" {
  type = map(string)
  default = {
    dev   = "westeurope"
    stage = "westeurope"
    prod  = "westeurope"
  }
}

variable "enable_policy_assignment" {
  type = map(bool)
  default = {
    dev   = false
    stage = true
    prod  = false
  }
}

variable "dns_zone_name" {
  type = map(string)
  default = {
    dev   = "kafka.dev.payex.net"
    stage = "kafka.stage.payex.net"
    prod  = "kafka.payex.net"
  }
}

variable "loki_sa" {
  type = map(string)
  default = {
    dev   = "devswplokisa"
    stage = "stagelokisa"
    prod  = "prodlokisa"
  }
}

variable "bims_mdd_raw_producer_sas_start_time" {
  type        = string
  description = "Start time for the bims_mdd_raw_producer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_mdd_raw_producer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_mdd_raw_producer_sas token."
  default     = "2028-03-18T00:00:00Z"
}
variable "bims_mdd_raw_consumer_sas_start_time" {
  type        = string
  description = "Start time for the bims_mdd_raw_consumer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_mdd_raw_consumer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_mdd_raw_consumer_sas token."
  default     = "2028-03-18T00:00:00Z"
}

variable "bims_add_raw_producer_sas_start_time" {
  type        = string
  description = "Start time for the bims_add_raw_producer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_add_raw_producer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_add_raw_producer_sas token."
  default     = "2028-03-18T00:00:00Z"
}
variable "bims_add_raw_consumer_sas_start_time" {
  type        = string
  description = "Start time for the bims_add_raw_consumer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_add_raw_consumer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_add_raw_consumer_sas token."
  default     = "2028-03-18T00:00:00Z"
}

variable "bims_pdd_raw_producer_sas_start_time" {
  type        = string
  description = "Start time for the bims_pdd_raw_producer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_pdd_raw_producer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_pdd_raw_producer_sas token."
  default     = "2028-03-18T00:00:00Z"
}
variable "bims_pdd_raw_consumer_sas_start_time" {
  type        = string
  description = "Start time for the bims_pdd_raw_consumer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_pdd_raw_consumer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_pdd_raw_consumer_sas token."
  default     = "2028-03-18T00:00:00Z"
}

variable "bims_bundles_raw_producer_sas_start_time" {
  type        = string
  description = "Start time for the bims_bundles_raw_producer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_bundles_raw_producer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_bundles_raw_producer_sas token."
  default     = "2028-03-18T00:00:00Z"
}
variable "bims_bundles_raw_consumer_sas_start_time" {
  type        = string
  description = "Start time for the bims_bundles_raw_consumer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_bundles_raw_consumer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_bundles_raw_consumer_sas token."
  default     = "2028-03-18T00:00:00Z"
}

variable "bims_exceptions_raw_producer_sas_start_time" {
  type        = string
  description = "Start time for the bims_exceptions_raw_producer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_exceptions_raw_producer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_exceptions_raw_producer_sas token."
  default     = "2028-03-18T00:00:00Z"
}
variable "bims_exceptions_raw_consumer_sas_start_time" {
  type        = string
  description = "Start time for the bims_exceptions_raw_consumer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_exceptions_raw_consumer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_exceptions_raw_consumer_sas token."
  default     = "2028-03-18T00:00:00Z"
}

variable "bims_elsa_raw_producer_sas_start_time" {
  type        = string
  description = "Start time for the bims_elsa_raw_producer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_elsa_raw_producer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_elsa_raw_producer_sas token."
  default     = "2028-03-18T00:00:00Z"
}
variable "bims_elsa_raw_consumer_sas_start_time" {
  type        = string
  description = "Start time for the bims_elsa_raw_consumer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "bims_elsa_raw_consumer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the bims_elsa_raw_consumer_sas token."
  default     = "2028-03-18T00:00:00Z"
}

variable "base24_authorizations_raw_producer_sas_start_time" {
  type        = string
  description = "Start time for the base24_authorizations_raw_producer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "base24_authorizations_raw_producer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the base24_authorizations_raw_producer_sas token."
  default     = "2028-03-18T00:00:00Z"
}

variable "base24_authorizations_raw_consumer_sas_start_time" {
  type        = string
  description = "Start time for the base24_authorizations_raw_consumer_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "base24_authorizations_raw_consumer_sas_expiry_time" {
  type        = string
  description = "Expiry time for the base24_authorizations_raw_consumer_sas token."
  default     = "2028-03-18T00:00:00Z"
}
variable "adls_authorizations_sas_start_time" {
  type        = string
  description = "Start time for the adls_authorizations_sas token."
  default     = "2025-03-18T00:00:00Z"
}
variable "adls_authorizations_sas_expiry_time" {
  type        = string
  description = "Expiry time for the adls_authorizations_sas token."
  default     = "2028-03-18T00:00:00Z"
}
