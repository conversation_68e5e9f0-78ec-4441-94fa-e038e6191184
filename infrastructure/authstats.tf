# Reference to existing resource group
data "azurerm_resource_group" "authstats_rg" {
  name = local.sharedsvcs_rg
}

# Reference to existing storage account
data "azurerm_storage_account" "sharedsvcs_sa" {
  name                = local.sharedsvcs_sa
  resource_group_name = data.azurerm_resource_group.authstats_rg.name
}

# Create a User-Assigned Managed Identity
resource "azurerm_user_assigned_identity" "authstats_uai" {
  name                = "authstats_uai"
  resource_group_name = local.identity_rg
  location            = local.identity_location

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_federated_identity_credential" "authstats_fic" {
  name                = "authstats_fic"
  resource_group_name = local.identity_rg
  subject             = "system:serviceaccount:automate:workload-identity-sa"
  issuer              = azurerm_kubernetes_cluster.aks_cluster.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.authstats_uai.id
  audience            = ["api://AzureADTokenExchange"]
}

# Assign the Storage Blob Data Contributor role to the UAI
resource "azurerm_role_assignment" "authstats_ra" {
  scope                = data.azurerm_storage_account.sharedsvcs_sa.id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = azurerm_user_assigned_identity.authstats_uai.principal_id
}

# Create a storage container in the existing storage account
resource "azurerm_storage_container" "auth_stats" {
  name                  = "auth-stats"
  storage_account_name  = data.azurerm_storage_account.sharedsvcs_sa.name
  container_access_type = "private"
}