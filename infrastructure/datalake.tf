resource "azurerm_resource_group" "data_lake_rg" {
  count    = local.environment == "stage" ? 1 : 0
  name     = "data_lake_rg"
  location = local.location
}

resource "azurerm_storage_account" "data_lake_sa" {
  count                      = local.environment == "stage" ? 1 : 0
  name                       = "${local.environment}asdlsa"
  resource_group_name        = azurerm_resource_group.data_lake_rg[count.index].name
  location                   = azurerm_resource_group.data_lake_rg[count.index].location
  account_tier               = "Standard"
  account_replication_type   = "ZRS"
  account_kind               = "StorageV2"
  is_hns_enabled             = true
  https_traffic_only_enabled = true
  min_tls_version            = "TLS1_2"

  network_rules {
    bypass                     = ["AzureServices"]
    default_action             = "Deny"
    ip_rules                   = ["************", "*************/29"]
    virtual_network_subnet_ids = [azurerm_subnet.aks_nodepool_subnet.id]
  }

  lifecycle {
    ignore_changes = [tags]
  }
  depends_on = [azurerm_resource_group.data_lake_rg]
}

resource "azurerm_storage_data_lake_gen2_filesystem" "data_lake_fs" {
  count              = local.environment == "stage" ? 1 : 0
  name               = "default"
  storage_account_id = azurerm_storage_account.data_lake_sa[count.index].id
  depends_on         = [azurerm_storage_account.data_lake_sa]
}
resource "azurerm_user_assigned_identity" "data_lake_uami" {
  name                = "${local.environment}-data-lake-uami"
  resource_group_name = local.identity_rg
  location            = local.identity_location
  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

# Role assignment for Synapse System-Assigned Managed Identity
resource "azurerm_role_assignment" "data_lake_synapse_sami_ra" {
  count                = local.environment == "stage" ? 1 : 0
  scope                = azurerm_storage_account.data_lake_sa[count.index].id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = "747280fa-8f34-4dfe-ae00-79220099448c" # Managed Identity object ID from Synapse Workspacen. TODO Change this in prod
  depends_on           = [azurerm_storage_account.data_lake_sa]
}

resource "azurerm_role_assignment" "data_lake_fabric_sami_ra" {
  count                = local.environment == "stage" ? 1 : 0
  scope                = azurerm_storage_account.data_lake_sa[count.index].id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = "0feedd7f-5e33-4fdc-b46f-4e21627481b8" # Managed Identity object ID from Fabric Workspacen. TODO Change this in prod
  depends_on           = [azurerm_storage_account.data_lake_sa]
}

# Role assignment for User-Assigned Managed Identity
resource "azurerm_role_assignment" "data_lake_ra" {
  count                = local.environment == "stage" ? 1 : 0
  scope                = azurerm_storage_account.data_lake_sa[count.index].id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = azurerm_user_assigned_identity.data_lake_uami.principal_id
  depends_on           = [azurerm_storage_account.data_lake_sa]
}

# Federated Identity to let Flink write to ADLS
resource "azurerm_federated_identity_credential" "flink" {
  name                = "flink"
  resource_group_name = local.identity_rg
  subject             = "system:serviceaccount:kafka:flink-deployment-sa"
  issuer              = azurerm_kubernetes_cluster.aks_cluster.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.data_lake_uami.id
  audience            = ["api://AzureADTokenExchange"]

  depends_on = [azurerm_user_assigned_identity.data_lake_uami]
}

resource "azurerm_private_endpoint" "data_lake_endpoint" {
  count               = local.environment == "stage" ? 1 : 0
  name                = "${local.environment}-data-lake-endpoint"
  location            = local.location
  resource_group_name = azurerm_subnet.nodepool_subnet[0].resource_group_name
  subnet_id           = azurerm_subnet.nodepool_subnet[0].id

  private_service_connection {
    name                           = "${local.environment}-service-connection"
    is_manual_connection           = false
    private_connection_resource_id = azurerm_storage_account.data_lake_sa[0].id
    subresource_names              = ["dfs"]
  }
  private_dns_zone_group {
    name                 = "default"
    private_dns_zone_ids = ["/subscriptions/********-6c6e-477c-bc26-92eb442b90f9/resourceGroups/global-itops-infrastructure-01/providers/Microsoft.Network/privateDnsZones/privatelink.dfs.core.windows.net"]
  }

  depends_on = [
    azurerm_subnet_route_table_association.subnet_route_table_association,
    azurerm_subnet_network_security_group_association.default_subnet_network_security_group_association,
    azurerm_storage_account.data_lake_sa
  ]
  lifecycle {
    ignore_changes = [tags]
  }
}
