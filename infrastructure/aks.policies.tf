/*
resource "azurerm_resource_policy_assignment" "aks_policy_restrict_container_registries" {
  count                = local.enable_policy_assignment ? 1 : 0
  name                 = "aks-policy-restrict-container-registries"
  display_name         = "Restrict Container Registries"
  description          = "Kubernetes cluster containers should only use allowed images"
  policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/febd0533-8e55-448f-b837-bd0e06f16469"
  enforce              = true
  resource_id          = azurerm_kubernetes_cluster.aks_cluster.id

  parameters = jsonencode(
    {
      effect = {
        value = "deny"
      }
      excludedNamespaces = {
        value : [
          "argocd",
          "gatekeeper-system",
          "kube-node-lease",
          "kube-public",
          "kube-system"
        ]
      }
      excludedContainers = {
        value : [
          "busybox-secrets-store-inline",
          "busybox"
        ]
      }
      allowedContainerImagesRegex = {
        value = "^(ghcr\\.io/(payex|apache)|grafana|docker\\.io/(?:bitnami|grafana|library|provectuslabs|nginxinc|kiwigrid)/|registry\\.k8s\\.io/ingress-nginx|quay\\.io/(?:jetstack|kiwigrid|minio|prometheus-operator|prometheus|strimzi)/|registry\\.k8s\\.io/kube-state-metrics|prom|memcached).*$"
      }
    }
  )

  depends_on = [
    azurerm_kubernetes_cluster.aks_cluster
  ]
}
 */

resource "azurerm_resource_policy_assignment" "aks_policy_disallow_privileged_containers" {
  count                = local.enable_policy_assignment ? 1 : 0
  name                 = "aks-policy-disallow-privileged-containers"
  display_name         = "Disallow Privileged Containers"
  description          = "Do not allow privileged containers creation in a Kubernetes cluster"
  policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/95edb821-ddaf-4404-9732-666045e056b4"
  enforce              = true
  resource_id          = azurerm_kubernetes_cluster.aks_cluster.id

  parameters = jsonencode(
    {
      effect = {
        value = "deny"
      }
      excludedNamespaces = {
        value : [
          "argocd",
          "cert-manager",
          "ingress-nginx",
          "kafka"
        ]
      }
    }
  )

  depends_on = [
    azurerm_kubernetes_cluster.aks_cluster
  ]
}

resource "azurerm_resource_policy_assignment" "aks_policy_disallow_privilege_escalation" {
  count                = local.enable_policy_assignment ? 1 : 0
  name                 = "aks-policy-disallow-privilege-escalation"
  display_name         = "Disallow Privilege Escalation"
  description          = "Do not allow containers to run with privilege escalation to root in a Kubernetes cluster"
  policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/1c6e92c9-99f0-4e55-9cf2-0c234dc48f99"
  enforce              = true
  resource_id          = azurerm_kubernetes_cluster.aks_cluster.id

  parameters = jsonencode(
    {
      effect = {
        value = "deny"
      }
      excludedNamespaces = {
        value : [
          "argocd",
          "cert-manager",
          "ingress-nginx",
          "kafka",
          "monitoring"
        ]
      }
      excludedContainers = {
        value : [
          "busybox",
          "postgresql",
          "rabbitmq",
          "init-chown-data",
          "automate-pos",
          "automate-pba",
          "automate-tms"
        ]
      }
    }
  )

  depends_on = [
    azurerm_kubernetes_cluster.aks_cluster
  ]
}


resource "azurerm_resource_policy_assignment" "aks_policy_disallow_root_user" {
  count                = local.enable_policy_assignment ? 1 : 0
  name                 = "aks-policy-disallow-root-user"
  display_name         = "Disallow Root User"
  description          = "Control the user, primary group, supplemental group and file system group IDs that pods and containers can use to run in a Kubernetes Cluster"
  policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/f06ddb64-5fa3-4b77-b166-acb36f7f6042"
  enforce              = true
  resource_id          = azurerm_kubernetes_cluster.aks_cluster.id

  parameters = jsonencode(
    {
      effect = {
        value = "deny"
      }
      excludedNamespaces = {
        value : [
          "argocd",
          "cert-manager",
          "ingress-nginx",
          "kafka",
          "monitoring"
        ]
      }
      excludedContainers = {
        value : [
          "busybox",
          "postgresql",
          "rabbitmq",
          "kube-state-metrics",
          "init-chown-data",
          "automate-pos",
          "automate-pba",
          "automate-tms"
        ]
      }

    }
  )

  depends_on = [
    azurerm_kubernetes_cluster.aks_cluster
  ]
}

resource "azurerm_resource_policy_assignment" "aks_policy_enforce_readonly_root_filesystem" {
  count                = local.enable_policy_assignment ? 1 : 0
  name                 = "aks-policy-enforce-readonly-root-filesystem"
  display_name         = "Enforce Read-only Root Filesystem"
  description          = "Run containers with a read only root file system to protect from changes at run-time with malicious binaries being added to PATH in a Kubernetes cluster"
  policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/df49d893-a74c-421d-bc95-c663042e5b80"
  enforce              = true
  resource_id          = azurerm_kubernetes_cluster.aks_cluster.id

  parameters = jsonencode(
    {
      effect = {
        value = "deny"
      }
      excludedNamespaces = {
        value : [
          "argocd",
          "cert-manager",
          "ingress-nginx",
          "kafka",
          "monitoring"
        ]
      }
      excludedContainers = {
        value : [
          "busybox",
          "postgresql",
          "rabbitmq",
          "kube-state-metrics",
          "init-chown-data",
          "automate-pos",
          "automate-pba",
          "automate-tms"
        ]
      }
    }
  )

  depends_on = [
    azurerm_kubernetes_cluster.aks_cluster
  ]
}
