output "aks_rg_name" {
  value = azurerm_kubernetes_cluster.aks_cluster.resource_group_name
}

output "aks_cluster_name" {
  value = azurerm_kubernetes_cluster.aks_cluster.name
}

output "aks_loadbalancer_ip_address" {
  value = azurerm_kubernetes_cluster.aks_cluster.network_profile[0].load_balancer_profile[0].effective_outbound_ips
}

output "federated_identity_credentials" {
  value = azurerm_federated_identity_credential.federated_identity_credentials[*].id
}
