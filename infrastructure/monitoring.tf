resource "azurerm_resource_group" "loki_rg" {
  name     = local.loki_rg
  location = local.location
}

resource "azurerm_storage_account" "loki_sa" {
  name                     = local.loki_sa
  location                 = azurerm_resource_group.loki_rg.location
  resource_group_name      = azurerm_resource_group.loki_rg.name
  account_tier             = "Standard"
  account_replication_type = "ZRS"

  tags = {
    environment = local.environment
  }

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_user_assigned_identity" "loki_uai" {
  name                = "loki_uai"
  resource_group_name = local.identity_rg
  location            = local.identity_location

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_role_assignment" "loki_ra" {
  scope                = azurerm_storage_account.loki_sa.id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = azurerm_user_assigned_identity.loki_uai.principal_id
}

resource "azurerm_federated_identity_credential" "loki_fic" {
  name                = "loki_fic"
  resource_group_name = local.identity_rg
  subject             = "system:serviceaccount:monitoring:loki"
  issuer              = azurerm_kubernetes_cluster.aks_cluster.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.loki_uai.id
  audience            = ["api://AzureADTokenExchange"]
}

resource "azurerm_storage_container" "loki_chunks" {
  name                  = "loki-chunks"
  storage_account_name  = azurerm_storage_account.loki_sa.name
  container_access_type = "private"
}
resource "azurerm_storage_container" "loki_ruler" {
  name                  = "loki-ruler"
  storage_account_name  = azurerm_storage_account.loki_sa.name
  container_access_type = "private"
}

resource "azurerm_storage_container" "loki_admin" {
  name                  = "loki-admin"
  storage_account_name  = azurerm_storage_account.loki_sa.name
  container_access_type = "private"
}

resource "azurerm_federated_identity_credential" "tempo_fic" {
  name                = "tempo_fic"
  resource_group_name = local.identity_rg
  subject             = "system:serviceaccount:monitoring:tempo"
  issuer              = azurerm_kubernetes_cluster.aks_cluster.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.loki_uai.id
  audience            = ["api://AzureADTokenExchange"]
}

resource "azurerm_storage_container" "tempo" {
  name                  = "tempo-traces"
  storage_account_name  = azurerm_storage_account.loki_sa.name
  container_access_type = "private"
}
