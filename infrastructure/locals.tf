locals {
  environment                 = terraform.workspace
  systemname                  = lookup(var.systemname, terraform.workspace, null)
  location                    = lookup(var.location, terraform.workspace, null)
  private_dns_zone_id         = lookup(var.private_dns_zone_id, terraform.workspace, null)
  aks_sku_tier                = lookup(var.aks_sku_tier, terraform.workspace, null)
  aks_cluster_name            = "${local.environment}-${local.systemname}-aks"
  dns_prefix                  = "${local.environment}-${local.systemname}-aks"
  cluster_rg                  = "${local.environment}-${local.systemname}-aks-rg"
  alerts_rg                   = "${local.environment}-${local.systemname}-monitoring-alerts-rg"
  secret_expiry_rg            = "${local.environment}-${local.systemname}-secret-expiry-rg"
  vnet_rg                     = lookup(var.existing_vnet_rg_name, terraform.workspace, null)
  vnet_name                   = lookup(var.existing_vnet_name, terraform.workspace, null)
  aks_default_address_space   = lookup(var.aks_default_address_space, terraform.workspace, null)
  aks_address_space           = lookup(var.aks_address_space, terraform.workspace, null)
  nodepool_rg                 = "${local.environment}-${local.systemname}-aks-nodepool-rg"
  nodepool_rt_name            = "${local.environment}-${local.systemname}-rt"
  kafka_nodepool_nsg_name     = "${local.environment}-${local.systemname}-kafka-nsg"
  general_nodepool_count      = lookup(var.general_nodepool_count, terraform.workspace, null)
  general_nodepool_vm_size    = lookup(var.general_nodepool_vm_size, terraform.workspace, null)
  general_nodepool_zones      = lookup(var.general_nodepool_zones, terraform.workspace, null)
  sharedsvcs_rg               = lookup(var.existing_sharedsvcs_name_rg, terraform.workspace, null)
  sharedsvcs_sa               = lookup(var.existing_sharedsvcs_name_sa, terraform.workspace, null)
  key_vault_rg                = lookup(var.existing_sharedsvcs_name_rg, terraform.workspace, null)
  key_vault_name              = lookup(var.existing_key_vault_name, terraform.workspace, null)
  identity_name               = "${local.environment}-${local.systemname}-uai"
  identity_rg                 = lookup(var.existing_sharedsvcs_name_rg, terraform.workspace, null)
  identity_location           = lookup(var.identity_location, terraform.workspace, null)
  federated_identities        = lookup(var.federated_identities, terraform.workspace, null)
  federated_identity_names    = formatlist("${local.environment}-${local.systemname}-%s-fi", local.federated_identities)
  federated_identity_subjects = formatlist("system:serviceaccount:%s:workload-identity-sa", local.federated_identities)
  enable_policy_assignment    = lookup(var.enable_policy_assignment, terraform.workspace, null)
  dns_zone_rg                 = "${local.environment}-${local.systemname}-dns-zone-rg"
  dns_zone_name               = lookup(var.dns_zone_name, terraform.workspace, null)
  claimcheck_rg               = "${local.environment}-claimcheck-rg"
  claimcheck_sa               = "${local.environment}claimchecksa"
  loki_rg                     = "${local.environment}-loki-rg"
  loki_sa                     = lookup(var.loki_sa, terraform.workspace, null)

  # fabric
  # create_fabric      = local.environment == "stage" ? 1 : 0
  create_fabric      = 0
  fabric_rg          = "${local.environment}_fabric_rg"
  fabric_basename    = local.environment
  fabric_sku         = "F2"
  fabric_admin_email = "c2282494-ebf6-4020-b13e-b47fff3a01a0"

  aks_upgrade_override_effective_until_date = timeadd(timestamp(), "336h")
}
