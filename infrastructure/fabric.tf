resource "azurerm_resource_group" "fabric_rg" {
  count    = local.create_fabric
  name     = local.fabric_rg
  location = local.location
}

resource "azurerm_fabric_capacity" "fabric_fc" {
  count               = local.create_fabric
  name                = "${local.fabric_basename}ffc"
  location            = local.location
  resource_group_name = azurerm_resource_group.fabric_rg[0].name

  administration_members = [data.azurerm_client_config.current.object_id]

  sku {
    name = local.fabric_sku
    tier = "Fabric"
  }

  tags = {
    environment = local.environment
  }
  # i prod
  # lifecycle {
  #   prevent_destroy = true
  # }
}
