resource "azurerm_user_assigned_identity" "user_assigned_identity" {
  name                = local.identity_name
  resource_group_name = local.identity_rg
  location            = local.identity_location

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_role_assignment" "keyvault_secrets_user" {
  principal_id         = azurerm_user_assigned_identity.user_assigned_identity.principal_id
  role_definition_name = "Key Vault Secrets User"
  scope                = data.azurerm_key_vault.keyvault.id

  depends_on = [
    azurerm_user_assigned_identity.user_assigned_identity
  ]
}

resource "azurerm_role_assignment" "crypto_officer" {
  role_definition_name = "Key Vault Crypto Officer"
  scope                = data.azurerm_key_vault.keyvault.id
  principal_id         = azurerm_user_assigned_identity.user_assigned_identity.principal_id

  depends_on = [
    azurerm_user_assigned_identity.user_assigned_identity
  ]
}

resource "azurerm_role_assignment" "crypto_service_encryption_user" {
  role_definition_name = "Key Vault Crypto Service Encryption User"
  scope                = data.azurerm_key_vault.keyvault.id
  principal_id         = azurerm_user_assigned_identity.user_assigned_identity.principal_id

  depends_on = [
    azurerm_user_assigned_identity.user_assigned_identity
  ]
}

resource "azurerm_role_assignment" "secrets_officer" {
  role_definition_name = "Key Vault Secrets Officer"
  scope                = data.azurerm_key_vault.keyvault.id
  principal_id         = azurerm_user_assigned_identity.user_assigned_identity.principal_id

  depends_on = [
    azurerm_user_assigned_identity.user_assigned_identity
  ]
}
resource "azurerm_role_assignment" "certificates_officer" {
  role_definition_name = "Key Vault Certificates Officer"
  scope                = data.azurerm_key_vault.keyvault.id
  principal_id         = azurerm_user_assigned_identity.user_assigned_identity.principal_id

  depends_on = [
    azurerm_user_assigned_identity.user_assigned_identity
  ]
}

resource "azurerm_role_assignment" "dns_zone_contributor" {
  role_definition_name = "DNS Zone Contributor"
  scope                = azurerm_dns_zone.dns_zone.id
  principal_id         = azurerm_user_assigned_identity.user_assigned_identity.principal_id

  depends_on = [
    azurerm_user_assigned_identity.user_assigned_identity
  ]
}

resource "azurerm_role_assignment" "reader" {
  role_definition_name = "Reader"
  scope                = azurerm_resource_group.aks-rg.id
  principal_id         = azurerm_user_assigned_identity.user_assigned_identity.principal_id

  depends_on = [
    azurerm_user_assigned_identity.user_assigned_identity
  ]
}

resource "azurerm_federated_identity_credential" "federated_identity_credentials" {
  count               = length(local.federated_identity_names)
  name                = local.federated_identity_names[count.index]
  resource_group_name = local.sharedsvcs_rg
  subject             = local.federated_identity_subjects[count.index]
  issuer              = azurerm_kubernetes_cluster.aks_cluster.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.user_assigned_identity.id
  audience            = ["api://AzureADTokenExchange"]
}

resource "azurerm_federated_identity_credential" "cert_manager" {
  name                = "cert-manager"
  resource_group_name = local.identity_rg
  subject             = "system:serviceaccount:cert-manager:cert-manager"
  issuer              = azurerm_kubernetes_cluster.aks_cluster.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.user_assigned_identity.id
  audience            = ["api://AzureADTokenExchange"]
}

resource "azurerm_federated_identity_credential" "external_dns" {
  name                = "external-dns"
  resource_group_name = local.identity_rg
  subject             = "system:serviceaccount:cert-manager:external-dns"
  issuer              = azurerm_kubernetes_cluster.aks_cluster.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.user_assigned_identity.id
  audience            = ["api://AzureADTokenExchange"]
}

