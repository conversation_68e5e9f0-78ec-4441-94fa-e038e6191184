resource "azurerm_resource_group" "alerts_rg" {
  name     = local.alerts_rg
  location = local.location
  tags = {
    environment = local.environment
    purpose     = "Monitoring Alerts and Actions"
  }
}
# -------------------------------------------------------------
# 1. Define the Action Group with the Slack Email Receiver
# -------------------------------------------------------------

resource "azurerm_monitor_action_group" "slack_email_action_group" {
  name                = "ag-slack-channel-email-notify"
  resource_group_name = local.alerts_rg
  short_name          = "slackemail"

  email_receiver {
    name                    = "sendToDataPlatformSlackChannel"
    email_address           = var.slack_channel_email[local.environment] # Value provided externally
    use_common_alert_schema = true
  }

  tags = {
    notification_target = "Slack Email"
  }
  lifecycle {
    ignore_changes = [tags]
  }

}

# -------------------------------------------------------------
# 2. Define the Activity Log Alert Rules
# -------------------------------------------------------------

# Alert for Deleting Policy Assignments
resource "azurerm_monitor_activity_log_alert" "delete-policy-assignment-alert" {
  name                = "delete_policy_assignment_alert"
  resource_group_name = local.alerts_rg
  location            = var.alert_rule_location[local.environment]
  scopes              = ["/subscriptions/${data.azurerm_client_config.current.subscription_id}"]
  description         = "Alert for Delete Policy Assignment operations"
  enabled             = true

  criteria {
    category       = "Administrative"
    operation_name = "Microsoft.Authorization/policyAssignments/delete"
    status         = "Succeeded"
    levels         = ["Verbose", "Informational", "Warning", "Error", "Critical"]
  }

  action {
    action_group_id = azurerm_monitor_action_group.slack_email_action_group.id
  }

  lifecycle {
    ignore_changes = [tags]
  }
}

# Alert for Creating/Updating Policy Assignments
resource "azurerm_monitor_activity_log_alert" "create-policy-assignment-alert" {
  name                = "create_policy_assignment_alert"
  resource_group_name = local.alerts_rg
  location            = var.alert_rule_location[local.environment]
  scopes              = ["/subscriptions/${data.azurerm_client_config.current.subscription_id}"]
  description         = "Alert for Create or Update Policy Assignment operations"
  enabled             = true

  criteria {
    category       = "Administrative"
    operation_name = "Microsoft.Authorization/policyAssignments/write" # Changed operation
    status         = "Succeeded"
    levels         = ["Verbose", "Informational", "Warning", "Error", "Critical"]
  }

  action {
    action_group_id = azurerm_monitor_action_group.slack_email_action_group.id
  }

  lifecycle {
    ignore_changes = [tags]
  }
}

# Alert for Creating/Updating Network Security Groups (NSG)
resource "azurerm_monitor_activity_log_alert" "create-update-nsg-alert" {
  name                = "create_update_nsg_alert"
  resource_group_name = local.alerts_rg
  location            = var.alert_rule_location[local.environment]
  scopes              = ["/subscriptions/${data.azurerm_client_config.current.subscription_id}"]
  description         = "Alert for Create or Update Network Security Group operations"
  enabled             = true

  criteria {
    category       = "Administrative"
    operation_name = "Microsoft.Network/networkSecurityGroups/write" # Changed operation
    status         = "Succeeded"
    levels         = ["Verbose", "Informational", "Warning", "Error", "Critical"]
  }

  action {
    action_group_id = azurerm_monitor_action_group.slack_email_action_group.id
  }

  lifecycle {
    ignore_changes = [tags]
  }
}

# Alert for Deleting Network Security Groups (NSG)
resource "azurerm_monitor_activity_log_alert" "delete-nsg-alert" {
  name                = "delete_nsg_alert"
  resource_group_name = local.alerts_rg
  location            = var.alert_rule_location[local.environment]
  scopes              = ["/subscriptions/${data.azurerm_client_config.current.subscription_id}"]
  description         = "Alert for Delete Network Security Group operations"
  enabled             = true

  criteria {
    category       = "Administrative"
    operation_name = "Microsoft.Network/networkSecurityGroups/delete" # Changed operation
    status         = "Succeeded"
    levels         = ["Verbose", "Informational", "Warning", "Error", "Critical"]
  }

  action {
    action_group_id = azurerm_monitor_action_group.slack_email_action_group.id
  }

  lifecycle {
    ignore_changes = [tags]
  }
}

# Alert for Creating/Updating Network Security Group (NSG) Rules
resource "azurerm_monitor_activity_log_alert" "create-update-nsg-rule-alert" {
  name                = "create_update_nsg_rule_alert"
  resource_group_name = local.alerts_rg
  location            = var.alert_rule_location[local.environment]
  scopes              = ["/subscriptions/${data.azurerm_client_config.current.subscription_id}"]
  description         = "Alert for Create or Update Network Security Group Rule operations"
  enabled             = true

  criteria {
    category       = "Administrative"
    operation_name = "Microsoft.Network/networkSecurityGroups/securityRules/write" # Changed operation
    status         = "Succeeded"
    levels         = ["Verbose", "Informational", "Warning", "Error", "Critical"]
  }

  action {
    action_group_id = azurerm_monitor_action_group.slack_email_action_group.id
  }

  lifecycle {
    ignore_changes = [tags]
  }
}

# Alert for Deleting Network Security Group (NSG) Rules
resource "azurerm_monitor_activity_log_alert" "delete-nsg-rule-alert" {
  name                = "delete_nsg_rule_alert"
  resource_group_name = local.alerts_rg
  location            = var.alert_rule_location[local.environment]
  scopes              = ["/subscriptions/${data.azurerm_client_config.current.subscription_id}"]
  description         = "Alert for Delete Network Security Group Rule operations"
  enabled             = true

  criteria {
    category       = "Administrative"
    operation_name = "Microsoft.Network/networkSecurityGroups/securityRules/delete" # Changed operation
    status         = "Succeeded"
    levels         = ["Verbose", "Informational", "Warning", "Error", "Critical"]
  }

  action {
    action_group_id = azurerm_monitor_action_group.slack_email_action_group.id
  }

  lifecycle {
    ignore_changes = [tags]
  }
}
