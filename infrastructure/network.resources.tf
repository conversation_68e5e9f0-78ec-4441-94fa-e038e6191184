# Subnet
resource "azurerm_route_table" "nodepool-rt" {
  name                = local.nodepool_rt_name
  location            = local.location
  resource_group_name = data.azurerm_virtual_network.vnet.resource_group_name
  route {
    name                   = "default"
    address_prefix         = "0.0.0.0/0"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "*********"
  }
  route {
    name                   = "LZ1-SWE"
    address_prefix         = "*********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "*********"
  }
  route {
    name                   = "LZ2-SWE"
    address_prefix         = "*********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "*********"
  }
  route {
    name                   = "GLOBAL-WEU"
    address_prefix         = "*********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "*********"
  }
  route {
    name                   = "PROD-SWE"
    address_prefix         = "*********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "*********"
  }
  route {
    name                   = "DEV-SWE"
    address_prefix         = "*********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "*********"
  }
  route {
    name                   = "IS-SWE"
    address_prefix         = "*********/16"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = "*********"
  }
  lifecycle {
    ignore_changes = [
      tags
    ]
  }

}

resource "azurerm_subnet" "nodepool_subnet" {
  count                = local.environment == "dev" ? 0 : 1
  name                 = "nodepool"
  resource_group_name  = data.azurerm_virtual_network.vnet.resource_group_name
  virtual_network_name = data.azurerm_virtual_network.vnet.name
  address_prefixes     = [local.aks_default_address_space]
}

resource "azurerm_network_security_group" "default_network_security_group" {
  name                = local.kafka_nodepool_nsg_name
  location            = local.location
  resource_group_name = data.azurerm_virtual_network.vnet.resource_group_name

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

moved {
  from = azurerm_network_security_group.kafka_nsg
  to   = azurerm_network_security_group.default_network_security_group
}

resource "azurerm_subnet_network_security_group_association" "default_subnet_network_security_group_association" {
  count                     = local.environment == "dev" ? 0 : 1
  subnet_id                 = azurerm_subnet.nodepool_subnet[count.index].id
  network_security_group_id = azurerm_network_security_group.default_network_security_group.id
}
moved {
  from = azurerm_subnet_network_security_group_association.kafka_nsg[0]
  to   = azurerm_subnet_network_security_group_association.default_subnet_network_security_group_association[0]
}

resource "azurerm_subnet_route_table_association" "subnet_route_table_association" {
  count          = local.environment == "dev" ? 0 : 1
  subnet_id      = azurerm_subnet.nodepool_subnet[count.index].id
  route_table_id = azurerm_route_table.nodepool-rt.id
}

moved {
  from = azurerm_subnet_route_table_association.kafka_rt[0]
  to   = azurerm_subnet_route_table_association.subnet_route_table_association[0]
}
# new nodepool stuff
resource "azurerm_subnet" "aks_nodepool_subnet" {
  name                 = "aks_nodepool_subnet"
  resource_group_name  = data.azurerm_virtual_network.vnet.resource_group_name
  virtual_network_name = data.azurerm_virtual_network.vnet.name
  address_prefixes     = [local.aks_address_space]
  service_endpoints    = ["Microsoft.Storage"]
}

resource "azurerm_subnet_network_security_group_association" "aks_nsg" {
  subnet_id                 = azurerm_subnet.aks_nodepool_subnet.id
  network_security_group_id = azurerm_network_security_group.default_network_security_group.id
}

resource "azurerm_subnet_route_table_association" "aks_rt" {
  subnet_id      = azurerm_subnet.aks_nodepool_subnet.id
  route_table_id = azurerm_route_table.nodepool-rt.id
}

