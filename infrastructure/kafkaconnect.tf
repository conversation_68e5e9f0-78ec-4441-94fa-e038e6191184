resource "azurerm_resource_group" "claimcheck_rg" {
  name     = local.claimcheck_rg
  location = local.location
}

resource "azurerm_storage_account" "claimcheck_sa" {
  name                     = local.claimcheck_sa
  location                 = azurerm_resource_group.claimcheck_rg.location
  resource_group_name      = azurerm_resource_group.claimcheck_rg.name
  account_tier             = "Standard"
  account_replication_type = "ZRS"

  tags = {
    environment = local.environment
  }

  lifecycle {
    ignore_changes = [
      tags
    ]
  }

}

resource "azurerm_user_assigned_identity" "data_platform_connect_cluster_uai" {
  name                = "${local.environment}-data-platform-connect-uai"
  resource_group_name = local.identity_rg
  location            = local.identity_location
  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_role_assignment" "data_platform_connect_cluster_ra" {
  scope                = azurerm_storage_account.claimcheck_sa.id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = azurerm_user_assigned_identity.data_platform_connect_cluster_uai.principal_id
}

resource "azurerm_federated_identity_credential" "data_platform_connect_cluster_fic" {
  name                = "data-platform-connect-cluster-fic"
  resource_group_name = local.identity_rg
  subject             = "system:serviceaccount:kafka:data-platform-connect-cluster-connect"
  issuer              = azurerm_kubernetes_cluster.aks_cluster.oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.data_platform_connect_cluster_uai.id
  audience            = ["api://AzureADTokenExchange"]
}

resource "azurerm_storage_container" "base24_authorizations_raw" {
  name                  = "base24-authorizations-raw"
  storage_account_name  = azurerm_storage_account.claimcheck_sa.name
  container_access_type = "private"
}

resource "azurerm_storage_management_policy" "base24_authorizations_raw_lifecycle" {
  storage_account_id = azurerm_storage_account.claimcheck_sa.id

  rule {
    name    = "delete-old-files"
    enabled = true
    filters {
      blob_types = ["blockBlob"]
    }
    actions {
      base_blob {
        delete_after_days_since_creation_greater_than = 1
      }
    }
  }
}

data "azurerm_storage_account_blob_container_sas" "base24_authorizations_raw_producer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.base24_authorizations_raw.name
  https_only        = true

  start  = var.base24_authorizations_raw_producer_sas_start_time
  expiry = var.base24_authorizations_raw_producer_sas_expiry_time
  permissions {
    read   = true
    add    = true
    create = true
    write  = true
    delete = true
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "base24_authorizations_raw_producer_sas_secret" {
  name            = "base24-authorizations-raw-producer-sas"
  value           = data.azurerm_storage_account_blob_container_sas.base24_authorizations_raw_producer_sas.sas
  key_vault_id    = data.azurerm_key_vault.keyvault.id
  expiration_date = var.base24_authorizations_raw_producer_sas_expiry_time
}

data "azurerm_storage_account_blob_container_sas" "base24_authorizations_raw_consumer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.base24_authorizations_raw.name
  https_only        = true

  start  = var.base24_authorizations_raw_consumer_sas_start_time
  expiry = var.base24_authorizations_raw_consumer_sas_expiry_time
  permissions {
    read   = true
    add    = false
    create = false
    write  = false
    delete = false
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "base24_authorizations_raw_consumer_sas_secret" {
  name            = "base24-authorizations-raw-consumer-sas"
  value           = data.azurerm_storage_account_blob_container_sas.base24_authorizations_raw_consumer_sas.sas
  key_vault_id    = data.azurerm_key_vault.keyvault.id
  expiration_date = var.base24_authorizations_raw_consumer_sas_expiry_time
}

// MDD

resource "azurerm_storage_container" "bims_mdd_raw" {
  name                  = "bims-mdd-raw"
  storage_account_name  = azurerm_storage_account.claimcheck_sa.name
  container_access_type = "private"
}

# Producer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_mdd_raw_producer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_mdd_raw.name
  https_only        = true

  start  = var.bims_mdd_raw_producer_sas_start_time
  expiry = var.bims_mdd_raw_producer_sas_expiry_time
  permissions {
    read   = true
    add    = true
    create = true
    write  = true
    delete = true
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_mdd_raw_producer_sas_secret" {
  name            = "bims-mdd-raw-producer-sas"
  value           = data.azurerm_storage_account_blob_container_sas.bims_mdd_raw_producer_sas.sas
  key_vault_id    = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_mdd_raw_producer_sas_expiry_time
}

# Consumer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_mdd_raw_consumer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_mdd_raw.name
  https_only        = true

  start  = var.bims_mdd_raw_consumer_sas_start_time
  expiry = var.bims_mdd_raw_consumer_sas_expiry_time
  permissions {
    read   = true
    add    = false
    create = false
    write  = false
    delete = false
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_mdd_raw_consumer_sas_secret" {
  name            = "bims-mdd-raw-consumer-sas"
  value           = data.azurerm_storage_account_blob_container_sas.bims_mdd_raw_consumer_sas.sas
  key_vault_id    = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_mdd_raw_consumer_sas_expiry_time
}

// ADD

resource "azurerm_storage_container" "bims_add_raw" {
  name                  = "bims-add-raw"
  storage_account_name  = azurerm_storage_account.claimcheck_sa.name
  container_access_type = "private"
}

data "azurerm_storage_account_blob_container_sas" "bims_add_raw_producer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_add_raw.name
  https_only        = true

  start  = var.bims_add_raw_producer_sas_start_time
  expiry = var.bims_add_raw_producer_sas_expiry_time
  permissions {
    read   = true
    add    = true
    create = true
    write  = true
    delete = true
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_add_raw_producer_sas_secret" {
  name            = "bims-add-raw-producer-sas"
  value           = data.azurerm_storage_account_blob_container_sas.bims_add_raw_producer_sas.sas
  key_vault_id    = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_add_raw_producer_sas_expiry_time
}


data "azurerm_storage_account_blob_container_sas" "bims_add_raw_consumer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_add_raw.name
  https_only        = true

  start  = var.bims_add_raw_consumer_sas_start_time
  expiry = var.bims_add_raw_consumer_sas_expiry_time
  permissions {
    read   = true
    add    = false
    create = false
    write  = false
    delete = false
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_add_raw_consumer_sas_secret" {
  name            = "bims-add-raw-consumer-sas"
  value           = data.azurerm_storage_account_blob_container_sas.bims_add_raw_consumer_sas.sas
  key_vault_id    = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_add_raw_consumer_sas_expiry_time
}

// PDD

resource "azurerm_storage_container" "bims_pdd_raw" {
  name                  = "bims-pdd-raw"
  storage_account_name  = azurerm_storage_account.claimcheck_sa.name
  container_access_type = "private"
}

# Producer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_pdd_raw_producer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_pdd_raw.name
  https_only        = true

  start  = var.bims_pdd_raw_producer_sas_start_time
  expiry = var.bims_pdd_raw_producer_sas_expiry_time
  permissions {
    read   = true
    add    = true
    create = true
    write  = true
    delete = true
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_pdd_raw_producer_sas_secret" {
  name         = "bims-pdd-raw-producer-sas"
  value        = data.azurerm_storage_account_blob_container_sas.bims_pdd_raw_producer_sas.sas
  key_vault_id = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_pdd_raw_producer_sas_expiry_time
}

# Consumer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_pdd_raw_consumer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_pdd_raw.name
  https_only        = true

  start  = var.bims_pdd_raw_consumer_sas_start_time
  expiry = var.bims_pdd_raw_consumer_sas_expiry_time

  permissions {
    read   = true
    add    = false
    create = false
    write  = false
    delete = false
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_pdd_raw_consumer_sas_secret" {
  name         = "bims-pdd-raw-consumer-sas"
  value        = data.azurerm_storage_account_blob_container_sas.bims_pdd_raw_consumer_sas.sas
  key_vault_id = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_pdd_raw_consumer_sas_expiry_time
}

// Bundles

resource "azurerm_storage_container" "bims_bundles_raw" {
  name                  = "bims-bundles-raw"
  storage_account_name  = azurerm_storage_account.claimcheck_sa.name
  container_access_type = "private"
}

# Producer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_bundles_raw_producer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_bundles_raw.name
  https_only        = true

  start  = var.bims_bundles_raw_producer_sas_start_time
  expiry = var.bims_bundles_raw_producer_sas_expiry_time
  permissions {
    read   = true
    add    = true
    create = true
    write  = true
    delete = true
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_bundles_raw_producer_sas_secret" {
  name         = "bims-bundles-raw-producer-sas"
  value        = data.azurerm_storage_account_blob_container_sas.bims_bundles_raw_producer_sas.sas
  key_vault_id = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_bundles_raw_producer_sas_expiry_time
}

# Consumer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_bundles_raw_consumer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_bundles_raw.name
  https_only        = true

  start  = var.bims_bundles_raw_consumer_sas_start_time
  expiry = var.bims_bundles_raw_consumer_sas_expiry_time

  permissions {
    read   = true
    add    = false
    create = false
    write  = false
    delete = false
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_bundles_raw_consumer_sas_secret" {
  name         = "bims-bundles-raw-consumer-sas"
  value        = data.azurerm_storage_account_blob_container_sas.bims_bundles_raw_consumer_sas.sas
  key_vault_id = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_bundles_raw_consumer_sas_expiry_time
}


// Exceptions
resource "azurerm_storage_container" "bims_exceptions_raw" {
  name                  = "bims-exceptions-raw"
  storage_account_name  = azurerm_storage_account.claimcheck_sa.name
  container_access_type = "private"
}

# Producer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_exceptions_raw_producer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_exceptions_raw.name
  https_only        = true

  start  = var.bims_exceptions_raw_producer_sas_start_time
  expiry = var.bims_exceptions_raw_producer_sas_expiry_time
  permissions {
    read   = true
    add    = true
    create = true
    write  = true
    delete = true
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_exceptions_raw_producer_sas_secret" {
  name         = "bims-exceptions-raw-producer-sas"
  value        = data.azurerm_storage_account_blob_container_sas.bims_exceptions_raw_producer_sas.sas
  key_vault_id = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_exceptions_raw_producer_sas_expiry_time
}

# Consumer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_exceptions_raw_consumer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_exceptions_raw.name
  https_only        = true

  start  = var.bims_exceptions_raw_consumer_sas_start_time
  expiry = var.bims_exceptions_raw_consumer_sas_expiry_time

  permissions {
    read   = true
    add    = false
    create = false
    write  = false
    delete = false
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_exceptions_raw_consumer_sas_secret" {
  name         = "bims-exceptions-raw-consumer-sas"
  value        = data.azurerm_storage_account_blob_container_sas.bims_exceptions_raw_consumer_sas.sas
  key_vault_id = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_exceptions_raw_consumer_sas_expiry_time
}

// Elsa
resource "azurerm_storage_container" "bims_elsa_raw" {
  name                  = "bims-elsa-raw"
  storage_account_name  = azurerm_storage_account.claimcheck_sa.name
  container_access_type = "private"
}

# Producer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_elsa_raw_producer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_elsa_raw.name
  https_only        = true

  start  = var.bims_elsa_raw_producer_sas_start_time
  expiry = var.bims_elsa_raw_producer_sas_expiry_time
  permissions {
    read   = true
    add    = true
    create = true
    write  = true
    delete = true
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_elsa_raw_producer_sas_secret" {
  name         = "bims-elsa-raw-producer-sas"
  value        = data.azurerm_storage_account_blob_container_sas.bims_elsa_raw_producer_sas.sas
  key_vault_id = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_elsa_raw_producer_sas_expiry_time
}

# Consumer SAS Token

data "azurerm_storage_account_blob_container_sas" "bims_elsa_raw_consumer_sas" {
  connection_string = azurerm_storage_account.claimcheck_sa.primary_connection_string
  container_name    = azurerm_storage_container.bims_elsa_raw.name
  https_only        = true

  start  = var.bims_elsa_raw_consumer_sas_start_time
  expiry = var.bims_elsa_raw_consumer_sas_expiry_time

  permissions {
    read   = true
    add    = false
    create = false
    write  = false
    delete = false
    list   = true
  }

  cache_control       = "max-age=5"
  content_disposition = "inline"
  content_encoding    = "deflate"
  content_language    = "en-US"
  content_type        = "text/plain"
}

resource "azurerm_key_vault_secret" "bims_elsa_raw_consumer_sas_secret" {
  name         = "bims-elsa-raw-consumer-sas"
  value        = data.azurerm_storage_account_blob_container_sas.bims_elsa_raw_consumer_sas.sas
  key_vault_id = data.azurerm_key_vault.keyvault.id
  expiration_date = var.bims_elsa_raw_consumer_sas_expiry_time
}