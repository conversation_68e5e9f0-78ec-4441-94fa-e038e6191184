![GitHub](https://img.shields.io/badge/GitHub-181717.svg?style=for-the-badge&logo=GitHub&logoColor=white)
![azure](https://img.shields.io/badge/Microsoft%20Azure-0078D4.svg?style=for-the-badge&logo=Microsoft-Azure&logoColor=white)
![terraform](https://img.shields.io/badge/Terraform-7B42BC.svg?style=for-the-badge&logo=Terraform&logoColor=white)
![kubernetes](https://img.shields.io/badge/Kubernetes-326CE5.svg?style=for-the-badge&logo=Kubernetes&logoColor=white)


# tf-kafka-aks

Provisions an AKS cluster for Strimzi Kafka in a PayEx Azure Landing Zone. 

The provisioning consists of three steps: 

1. shared - a 'run once' step to extend the landing zone. Should never be deleted. 
2. infrastructure - creates resources needed for Kafka AKS
3. bootstrap - Installs secret providers, ArgoCD for GitOps and uses the app-of-apps pattern to bootstrap the AKS cluster.


## Preparation

Actions required before terraforming a new landing zone

### Create keys, certificates and secrets

We will use Azure Key Vault as our key management solution. In order for the terraforming to work we first need to add secrets, keys and certificates to our pre-built Azure landing zone. 


#### Script

Requirements: 

* LB\_IP\_ADDRESS
* GITHUB\_TOKEN\_FILE
* OCI\_TOKEN\_FILE
* DOCKER\_JSON\_FILE


```
bin/keyvault-secret-setup.sh

```

### Add environment specific Kafka topics and users

The Kafka topics and users are created with a per-environment specific Helm values.yaml file. Some applications might need these to be created in order to start correctly. 

### Update enviroment variables 

Update variables.tf with enviroment specific values. There is one for each provisioning step; shared, infrastructure, bootstrap.


## Terraform

### Setup Terraform to run using Azure Service Principal


```
az account set --subscription "0741761e-85cd-4e27-9182-81f6041e1862"

export ARM_SUBSCRIPTION_ID=`az account show|jq -r '.id'`
export ARM_TENANT_ID=`az account show|jq -r '.tenantId'`
export ARM_CLIENT_ID=`az keyvault secret show --vault-name dev-kafka-01-kv-18 --name spn-app-id-owner | jq -r '.value'`
export ARM_CLIENT_SECRET=`az keyvault secret show --vault-name dev-kafka-01-kv-18 --name spn-secret-owner | jq -r '.value'`
```
OR simply source the script setenv_arm.sh located in the bin folder. 

```
source bin/setenv_arm.sh [env]

```

If it is a completely new subscription/landing zone that is going to be provisioned you have to add the suubscription id to the setenv_arm.sh script.

To list available accounts (subscriptions) do the following:
```
az account list --output table
```

### Apply the configuration

The **tfstate** file is stored in a Storage Container. One for each step.

#### Step 1

```
cd shared
source ~/repos/payex/tf-kafka-aks/bin/setenv_arm.sh [env]
terraform plan
terraform apply -auto-approve
```
#### Step 2

```
cd infrastructure
source ~/repos/payex/tf-kafka-aks/bin/setenv_arm.sh [env]
terraform plan
terraform apply -auto-approve
```

#### Step 3

##### Load balancer

Get available ip address in AKS cluster. The address is to be used to create a Load Balancer in the AKS cluster using nginx ingress controller.

```
RG_VNET=$(az group list|jq -r '.[] .name'|grep vnet)
VNET_NAME=$(az network vnet list|jq -r '.[] .name')

az network vnet subnet list-available-ips --vnet-name $VNET_NAME --resource-group $RG_VNET --name nodepool
```

* Update bootstrap variables.tf loadBalancerIP with available ip address from command above
* argocd-apps - Add Helm chart values-[env].yaml in helm-charts-data-platform/argocd-apps and set ingressIpAddress and ingressHost
* data-platform-entities - Add Helm chart values-[env].yaml in helm-charts-data-platform/data-platform-entities
* Setup secrets in Azure Key Vault

##### Key vault
```
bin/keyvault-secret-setup.sh
```
##### Bootstrap

Set variable helm_registry_password to gain access to Helm chart repository.

```
cd bootstap
source ~/repos/payex/tf-kafka-aks/bin/setenv_arm.sh [env]
export OCI_TOKEN=`cat ~/repos/payex/helm-charts-data-platform/.secrets/oci.token`
terraform plan -auto-approve -var="helm_registry_password=$OCI_TOKEN"
terraform apply -auto-approve -var="helm_registry_password=$OCI_TOKEN"
```



## kubectl

Get kubeconfig for AKS cluster.

```
export K8S_ENV=dev

az account set --subscription "0741761e-85cd-4e27-9182-81f6041e1862"

az aks get-credentials \
  --resource-group "${K8S_ENV}-kafka-01-aks-rg" \
  --name "${K8S_ENV}-kafka-01-aks" \
  --admin

```
Verify that context is correct

```
kubectl config current-context

> dev-kafka-01-aks1-admin

```
Verify admin permissions to AKS cluster

```
kubectl cluster-info

> Kubernetes control plane is running at https://dev-kafka-01-aks1-1a62deb8.hcp.swedencentral.azmk8s.io:443
> CoreDNS is running at https://dev-kafka-01-aks1-1a62deb8.hcp.swedencentral.azmk8s.io:443/api/v1/namespaces/kube-system/services/kube-dns:dns/proxy
> Metrics-server is running at https://dev-kafka-01-aks1-1a62deb8.hcp.swedencentral.azmk8s.io:443/api/v1/namespaces/kube-system/services/https:metrics-server:/proxy

```

### Check K8s version

```
  ❯ az aks get-upgrades --resource-group stage-cloud-data-aks-rg --name stage-cloud-data-aks --output table
  
   Name     ResourceGroup            MasterVersion    Upgrades
   -------  -----------------------  ---------------  --------------
   default  stage-cloud-data-aks-rg  1.27.9           1.28.3, 1.28.5

```

### Manual upgrade of AKS-cluster

```
az aks upgrade --resource-group stage-cloud-data-aks-rg --name stage-cloud-data-aks --kubernetes-version 1.27.9
```
