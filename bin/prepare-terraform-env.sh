#!/bin/bash

ENVIRONMENT=${1:-dev}

case $ENVIRONMENT in

  dev)
    SUBSCRIPTION=c8b46846-3156-4220-8dfc-af0fafb8d2b0
    ;;
  test)
    SUBSCRIPTION=8eb62ac5-82ee-4b95-ab96-b8f2b0a4a654
    ;;
  stage)
    SUBSCRIPTION=********-7f22-4468-a34d-d61125f01797
    ;;
  prod)
    SUBSCRIPTION=38cb90f4-bf95-4b2c-b952-93f468d17aa0
    ;;
  *)
    echo "Unsupported env"
    exit 1
    ;;
esac

az account set --subscription $SUBSCRIPTION

RG_SHAREDSVCS=$(az group list|jq -r '.[] .name'|grep sharedsvcs)
RG_VNET=$(az group list|jq -r '.[] .name'|grep vnet)
STORAGE_ACCOUNT=$(az storage account list|jq -r '.[] .name')
KEY_VAULT=$(az keyvault list|jq -r '.[] .name')
VNET_NAME=$(az network vnet list|jq -r '.[] .name')

SUBNET_ADDRESS_PREFIX=$(az network vnet show --name $VNET_NAME --resource-group $RG_VNET --query "subnets [].addressPrefix"|grep -vE '\[|\]'|cut -d'"' -f 2)
AKS_ADDRESS_PREFIX=$(az network vnet show --name $VNET_NAME --resource-group $RG_VNET --query "addressSpace.addressPrefixes"|grep -vE '\[|\]'|cut -d'"' -f 2|grep -v $SUBNET_ADDRESS_PREFIX)

echo "Creating terraform backend config for $ENVIRONMENT..."

cat <<EOF >infrastructure/backend-$ENVIRONMENT.conf
container_name       = "terraform"
resource_group_name  = "$RG_SHAREDSVCS"
storage_account_name = "$STORAGE_ACCOUNT"
key                  = "$ENVIRONMENT-tf-kafka-aks-infrastructure.tfstate"
use_azuread_auth     = true
EOF

cat <<EOF >bootstrap/backend-$ENVIRONMENT.conf
container_name       = "terraform"
resource_group_name  = "$RG_SHAREDSVCS"
storage_account_name = "$STORAGE_ACCOUNT"
key                  = "$ENVIRONMENT-tf-kafka-aks-bootstrap.tfstate"
use_azuread_auth     = true
EOF

echo "\nThe following variables must be added manually in each variables.tf file: \n"

cat <<EOF
variable "existing_sharedsvcs_name_rg" {
  type = map(string)
  default = {
    $ENVIRONMENT = "$RG_SHAREDSVCS"
  }
}

variable "existing_key_vault_name" {
  type = map(string)
  default = {
    $ENVIRONMENT = "$KEY_VAULT"
  }
}

variable "existing_vnet_rg_name" {
  type = map(string)
  default = {
    $ENVIRONMENT = "$RG_VNET"
  }
}
variable "existing_vnet_name" {
  type = map(string)
  default = {
    $ENVIRONMENT = "$VNET_NAME"
  }
}
variable "aks_default_address_space" {
  type = map(string)
  default = {
    $ENVIRONMENT = "$AKS_ADDRESS_PREFIX"
  }
}
EOF
