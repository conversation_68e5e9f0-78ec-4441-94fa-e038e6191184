#!/bin/bash

az account set --subscription "0741761e-85cd-4e27-9182-81f6041e1862"

# deletes resources from dev that terraform failed to delete (for some reason)

if [ "$(az group exists --name dev-kafka-01-aks-rg)" = true ]; then
  echo "az group delete --resource-group dev-kafka-01-aks-rg --yes"
  az group delete --resource-group dev-kafka-01-aks-rg --yes
fi

if [ "$(az group exists --name dev-kafka-01-aks-nodepool-rg)" = true ]; then
  echo "az group delete --resource-group dev-kafka-01-aks-rg --yes"
  az group delete --resource-group dev-kafka-01-aks-rg --yes
fi

az disk-encryption-set delete --resource-group dev-kafka-01-sharedsvcs-rg --name aks-diskencryption
az keyvault secret delete --vault-name dev-kafka-01-kv-18 --name aks-diskencryption --output none
az keyvault secret delete --vault-name dev-kafka-01-kv-18 --name dev-kafka-01-kubeconfig --output none
az keyvault key delete --vault-name dev-kafka-01-kv-18 --name diskencryption --output none
az network nsg delete --name dev-kafka-01-nsg --resource-group dev-kafka-01-vnet-rg
az network route-table delete --resource-group dev-kafka-01-vnet-rg --name dev-kafka-01-rt
az network vnet subnet delete --name nodepool --resource-group dev-kafka-01-vnet-rg --vnet-name dev-kafka-01-vnet-01-18

