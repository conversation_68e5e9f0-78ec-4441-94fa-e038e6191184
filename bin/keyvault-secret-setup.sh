#!/bin/bash

usage () {
  echo "Usage: $0"
  echo "-e [environment]"
  echo "-v [view-secret]"
  echo "-a insight-settings-curity-secret"
  echo "-b insight-summary-curity-secret"
  echo "-c insight-notification-curity-secret"
  echo "-d docker-json-file"
  echo "-f firebase-json-file"
  echo "-g github-token-file"
  echo "-i insight-settings-pg-pwd"
  echo "-j insight-notification-pg-pwd"
  echo "-k key-name"
  echo "-l elasticsearch-api-key"
  echo "-m data-platform-cluster-curity"
  echo "-n curity-ca-cert"
  echo "-o oci-token-file"
  echo "-p insight-pg-pwd"
  echo "-q automate-efecte-curity"
  echo "-r automate-pba-curity"
  echo "-s elasticsearch-api-secret"
  echo "-t generate-tls-cer"
  echo "-u automate-pos-curity"
  echo "-w automate-tms-curity"
}

while getopts "a:b:c:d:e:f:g:i:j:k:l:m:n:o:p:r:q:s:t:u:v:w" opt; do
  case $opt in
    a ) INSIGHT_SETTINGS_CURITY_SECRET=$OPTARG;;
    b ) INSIGHT_SUMMARY_CURITY_SECRET=$OPTARG;;
    c ) INSIGHT_NOTIFICATION_CURITY_SECRET=$OPTARG;;
    d ) DOCKER_JSON_FILE=$OPTARG;;
    e ) ENVIRONMENT=$OPTARG;;
    f ) FIREBASE_JSON_FILE=$OPTARG;;
    g ) GITHUB_TOKEN_FILE=$OPTARG;;
    i ) INSIGHT_SETTINGS_PG_PWD=$OPTARG;;
    j ) INSIGHT_NOTIFICATION_PG_PWD=$OPTARG;;
    k ) KEY_NAME=$OPTARG;;
    l ) ELASTICSEARCH_API_KEY=$OPTARG;;
    m ) DATA_PLATFORM_CLUSTER_CURITY=$OPTARG;;
    n ) CURITY_CA_CERT=$OPTARG;;
    o ) OCI_TOKEN_FILE=$OPTARG;;
    p ) INSIGHT_PG_PWD=$OPTARG;;
    q ) AUTOMATE_EFECTE_CURITY=$OPTARG;;
    r ) AUTOMATE_PBA_CURITY=$OPTARG;;
    s ) ELASTICSEARCH_API_SECRET=$OPTARG;;
    t ) GENERATE_TLS_CERT="true";;
    u ) AUTOMATE_POS_CURITY=$OPTARG;;
    v ) VIEW_SECRETS="true";;
    w ) AUTOMATE_TMS_CURITY=$OPTARG;;
    h ) usage
    exit 1;;
  esac
done

case $ENVIRONMENT in

  dev)
    SUBSCRIPTION=c8b46846-3156-4220-8dfc-af0fafb8d2b0
    LB_IP_ADDRESS=************
    ;;
  test)
    SUBSCRIPTION=8eb62ac5-82ee-4b95-ab96-b8f2b0a4a654
    ;;
  stage)
    SUBSCRIPTION=********-7f22-4468-a34d-d61125f01797
    LB_IP_ADDRESS=***********
    ;;
  prod)
    SUBSCRIPTION=38cb90f4-bf95-4b2c-b952-93f468d17aa0
    LB_IP_ADDRESS=*************
    ;;
  *)
    echo "Unsupported env"
    exit 1
    ;;
esac

az account set --subscription $SUBSCRIPTION
az account show --query "name"

KEY_VAULT=$(az keyvault list|jq -r '.[] .name')
echo "Using key vault: $KEY_VAULT"

if [ "$VIEW_SECRETS" ]
then
  if [ "$KEY_NAME" ]
  then
    az keyvault secret show --vault-name $KEY_VAULT --name $KEY_NAME | jq -r '.value'
  else
    az keyvault secret list --vault-name $KEY_VAULT | jq -r '.[] .name'
  fi

fi

if [ "$DATA_PLATFORM_CLUSTER_CURITY" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n data-platform-cluster-curity --value $DATA_PLATFORM_CLUSTER_CURITY
fi

if [ "$AUTOMATE_EFECTE_CURITY" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n automate-efecte-curity --value $AUTOMATE_EFECTE_CURITY
fi

if [ "$AUTOMATE_PBA_CURITY" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n automate-pba-curity --value $AUTOMATE_PBA_CURITY
fi

if [ "$AUTOMATE_POS_CURITY" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n automate-pos-curity --value $AUTOMATE_POS_CURITY
fi

if [ "$AUTOMATE_TMS_CURITY" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n automate-tms-curity --value $AUTOMATE_TMS_CURITY
fi

if [ "$CURITY_CA_CERT" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n curity-ca-cert --value $CURITY_CA_CERT
fi

if [ "$INSIGHT_PG_PWD" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n insight-postgres-password --value $INSIGHT_PG_PWD
fi

if [ "$INSIGHT_SETTINGS_PG_PWD" ]
then

read -d '' SQL <<BLOCK
CREATE USER insight_settings WITH ENCRYPTED PASSWORD '$INSIGHT_SETTINGS_PG_PWD';
CREATE DATABASE insight_settings;
GRANT ALL PRIVILEGES ON DATABASE insight_settings TO insight_settings;
BLOCK

  az keyvault secret set --vault-name $KEY_VAULT -n insight-settings-postgresql-initdb --value "$SQL"
  az keyvault secret set --vault-name $KEY_VAULT -n insight-settings-postgresql-password --value "$INSIGHT_SETTINGS_PG_PWD"
fi

if [ "$INSIGHT_NOTIFICATION_PG_PWD" ]
then

read -d '' SQL <<BLOCK
CREATE USER insight_notification WITH ENCRYPTED PASSWORD '$INSIGHT_NOTIFICATION_PG_PWD';
CREATE DATABASE insight_notification;
GRANT ALL PRIVILEGES ON DATABASE insight_notification TO insight_notification;
BLOCK

  az keyvault secret set --vault-name $KEY_VAULT -n insight-notification-postgresql-initdb --value "$SQL"
  az keyvault secret set --vault-name $KEY_VAULT -n insight-notification-postgresql-password --value "$INSIGHT_NOTIFICATION_PG_PWD"
fi

if [ "$INSIGHT_SETTINGS_CURITY_SECRET" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n insight-settings-curity-secret --value $INSIGHT_SETTINGS_CURITY_SECRET
fi

if [ "$SHOW_SECRETS"]
then
  az keyvault secret show --vault-name $KEY_VAULT -n insight-settings-curity-secret
fi

if [ "$INSIGHT_SUMMARY_CURITY_SECRET" ]; then
  az keyvault secret set --vault-name $KEY_VAULT -n insight-summary-curity-secret --value="$INSIGHT_SUMMARY_CURITY_SECRET"
fi

if [ "$INSIGHT_NOTIFICATION_CURITY_SECRET" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n insight-notification-curity-secret --value $INSIGHT_NOTIFICATION_CURITY_SECRET
fi

if [ "$ELASTICSEARCH_API_KEY" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n elasticsearch-api-key --value $ELASTICSEARCH_API_KEY
fi

if [ "$ELASTICSEARCH_API_SECRET" ]
then
  az keyvault secret set --vault-name $KEY_VAULT -n elasticsearch-api-secret --value $ELASTICSEARCH_API_SECRET
fi

if [[ -f $FIREBASE_JSON_FILE ]] ; then
  az keyvault secret set --vault-name $KEY_VAULT -n firebase-service-account --content-type json --file $FIREBASE_JSON_FILE
fi

if [[ -f $GITHUB_TOKEN_FILE ]] ; then
  GITHUB_TOKEN=$(cat "$GITHUB_TOKEN_FILE")
  az keyvault secret set --vault-name $KEY_VAULT -n argocd-github-access-url --value "https://github.com/PayEx/helm-charts-data-platform"
  az keyvault secret set --vault-name $KEY_VAULT -n argocd-github-access-token --value $GITHUB_TOKEN
  az keyvault secret set --vault-name $KEY_VAULT -n argocd-github-access-username --value "not-used"
fi

if [[ -f $OCI_TOKEN_FILE ]] ; then
  OCI_TOKEN=$(cat "$OCI_TOKEN_FILE")
  az keyvault secret set --vault-name $KEY_VAULT -n argocd-oci-access-enable-oci --value "true"
  az keyvault secret set --vault-name $KEY_VAULT -n argocd-oci-access-type --value "helm"
  az keyvault secret set --vault-name $KEY_VAULT -n argocd-oci-access-url --value "ghcr.io/payex/helm-charts"
  az keyvault secret set --vault-name $KEY_VAULT -n argocd-oci-access-token --value $OCI_TOKEN
  az keyvault secret set --vault-name $KEY_VAULT -n argocd-oci-access-username --value "not-used"
fi

if [[ -f $DOCKER_JSON_FILE ]] ; then
  IMAGE_PULL_SECRET=$(cat "$DOCKER_JSON_FILE"|jq -c)
  az keyvault secret set --vault-name $KEY_VAULT -n insight-image-pull-secret --value $IMAGE_PULL_SECRET
  az keyvault secret set --vault-name $KEY_VAULT -n kafka-connect-image-pull-secret --value $IMAGE_PULL_SECRET
  az keyvault secret set --vault-name $KEY_VAULT -n kafka-connect-image-push-secret --value $IMAGE_PULL_SECRET
fi

if [ "$GENERATE_TLS_CERT" ]
then
TLS_CERT=$(az keyvault certificate show --vault-name $KEY_VAULT --name wildcard-payexnet-cert --query "name")

if [[ $TLS_CERT == "wildcard-payexnet-cert" ]]; then
  echo "wildcard-payexnet-cert exists. Exiting..."
  exit 0
fi

mkdir -p .secrets

rm .secrets/openssl.cnf 2> /dev/null

cat <<EOF >.secrets/openssl.cnf
[ v3_req ]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[ req ]
prompt                 = no
days                   = 365
distinguished_name     = req_distinguished_name
req_extensions         = v3_req

[ req_distinguished_name ]
commonName = payex.com
countryName = SE
stateOrProvinceName = Stockholm
organizationalUnitName = IT Department
organizationName = PayEx

keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = *.*.payex.com
DNS.2 = argocd.$LB_IP_ADDRESS.nip.io
DNS.3 = kafka-ui.1$LB_IP_ADDRESS.nip.io
EOF

openssl genrsa -out .secrets/rsa.key 4096 -passout pass:
openssl rsa -in .secrets/rsa.key -out .secrets/key.pem
openssl req -new -key .secrets/key.pem -config .secrets/openssl.cnf -out .secrets/request.csr
openssl x509 -req -extensions v3_req -in .secrets/request.csr -signkey .secrets/rsa.key -extfile .secrets/openssl.cnf -out .secrets/cert.pem
openssl pkcs12 -export -in .secrets/cert.pem -inkey .secrets/key.pem -out .secrets/wildcard-payexnet-cert.pfx -passout pass:

az keyvault certificate import --vault-name $KEY_VAULT -n wildcard-payexnet-cert -f .secrets/wildcard-payexnet-cert.pfx

fi





