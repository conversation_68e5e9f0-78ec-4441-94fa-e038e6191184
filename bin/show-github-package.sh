#!/bin/bash

CHART=${1:-data-platform-cluster}
USERNAME=${2:-not-used}
GITHUB_TOKEN=${3:-./.secrets/oci.token}
REGISTRY=ghcr.io/payex/helm-charts

while getopts "c:u:g:" opt; do
    case $opt in
        c ) CHART=$OPTARG;;
        u ) USERNAME=$OPTARG;;
        g ) GITHUB_TOKEN=$OPTARG;;
        *) usage
        exit 1;;
    esac
done

if [[ ! -f $GITHUB_TOKEN ]] ; then
    echo "File $GITHUB_TOKEN does not exist."
    exit
fi

< "$GITHUB_TOKEN" helm registry login ${REGISTRY} --username "${USERNAME}" --password-stdin

helm show all oci://${REGISTRY}/"${CHART}"

helm pull "oci://ghcr.io/payex/helm-charts/${CHART}" \
  --destination /tmp

helm registry logout ${REGISTRY}

ls -la "/tmp/${CHART}"*".tgz"