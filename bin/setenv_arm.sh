#!/bin/bash

echo "---| -------------------------------------------------------------------------------------- |---"
echo "---| Obs! Glöm ej att pimma dig som Storage Account Contributor och Key Vault Administrator |---"
echo "---| -------------------------------------------------------------------------------------- |---"

ENVIRONMENT=${1:-dev}

case $ENVIRONMENT in
  dev)
    SUBSCRIPTION=c8b46846-3156-4220-8dfc-af0fafb8d2b0
    ;;
  stage)
    SUBSCRIPTION=********-7f22-4468-a34d-d61125f01797
    ;;
  prod)
    SUBSCRIPTION=38cb90f4-bf95-4b2c-b952-93f468d17aa0
    ;;
  *)
    echo "Unsupported env: $ENVIRONMENT"
    return 1
    ;;
esac

echo "Using env: $ENVIRONMENT"

if [[ ! -f "backend-$ENVIRONMENT.conf" ]] ; then
    echo "File backend-$ENVIRONMENT.conf does not exist. Do a 'cd infrastructure'."
    exit 2
fi

az account set --subscription $SUBSCRIPTION
az account show --query "name"

STORAGE_ACCOUNT=$(az storage account list|jq -r '.[] .name'|grep clouddata)
KEY_VAULT=$(az keyvault list|jq -r '.[] .name')

CLIENT_ID=spn-app-id-owner
CLIENT_SECRET=spn-secret-owner

echo "STORAGE_ACCOUNT=$STORAGE_ACCOUNT"
echo "KEY_VAULT=$KEY_VAULT"

if test -z "$STORAGE_ACCOUNT" 
then
  STORAGE_ACCOUNT=$(az storage account list | jq -r '.[] .name')
fi

if test -z "$KEY_VAULT" 
then
  KEY_VAULT=$(az keyvault list | jq -r '.[] .name')
fi

export ARM_SUBSCRIPTION_ID=$SUBSCRIPTION
export ARM_TENANT_ID=$(az account show | jq -r '.tenantId')
export ARM_CLIENT_ID=$(az keyvault secret show --vault-name "$KEY_VAULT" --name "$CLIENT_ID" | jq -r '.value')
export ARM_CLIENT_SECRET=$(az keyvault secret show --vault-name "$KEY_VAULT" --name "$CLIENT_SECRET" | jq -r '.value')
export AZURE_STORAGE_ACCOUNT=$STORAGE_ACCOUNT
export AZURE_STORAGE_KEY=$(az storage account keys list --account-name "$STORAGE_ACCOUNT" | jq -r '.[] .value'|tail -1)

env | grep -E 'AZURE|ARM' | sort

echo
echo "AZ_LZ_AKS_CREDENTIALS: "
echo
echo "{ \"clientId\": \"$ARM_CLIENT_ID\", \"clientSecret\": \"$ARM_CLIENT_SECRET\", \"subscriptionId\": \"$ARM_SUBSCRIPTION_ID\", \"tenantId\": \"$ARM_TENANT_ID\"}" | jq

echo "terraform init -backend-config=backend-$ENVIRONMENT.conf"
echo "terraform workspace new $ENVIRONMENT"
echo "terraform workspace select $ENVIRONMENT"