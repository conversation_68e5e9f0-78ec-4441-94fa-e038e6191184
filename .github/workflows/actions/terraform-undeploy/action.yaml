name: Terraform Undeploy
description: |
  This action undeploys infrastructure with Terraform in Azure.

inputs:
  deploymentCredentials:
    description: The credentials used when undeploying to Azure
    required: true
  subscriptionId:
    description: The id of the subscription to undeploy the infrastructure to
    required: true
  workingDirectory:
    description: The directory where the Terraform configuration is located
    required: true
  environment:
    description: Environment to provision
    required: true
  dryRun:
    description: Run Terraform in dry-run mode (plan only)
    required: false
    default: false
    type: boolean

runs:
  using: composite
  steps:
    - name: 'Set subscription'
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        az account set --subscription ${{ inputs.subscriptionId }}

    - uses: hashicorp/setup-terraform@v3
      with:
        terraform_wrapper: false

    - name: 'Terraform > Init'
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        terraform init -backend-config="backend-${{ inputs.environment }}.conf"

    - uses: actions/download-artifact@v4
      with:
        name: "tfplan-aks-core-${{ github.run_id }}"
        path: "${{ inputs.workingDirectory }}"

    - name: 'Terraform > Workspace'
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        echo "Current environment: ${{ inputs.environment }}"
        terraform workspace new ${{ inputs.environment }}
        terraform workspace select ${{ inputs.environment }}

    - name: 'Terraform > Destroy'
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        if (${{ inputs.dryRun }} -eq 'true') {
          terraform plan -out=tfplan-aks-core
        } else {
          terraform apply "tfplan-aks-core"
        }
