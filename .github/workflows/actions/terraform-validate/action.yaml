name: Terraform Validate
description: |
  This action validates the Terraform configuration.
inputs:
  deploymentCredentials:
    description: The credentials used when deploying to Azure
    required: true
  subscriptionId:
    description: The id of the subscription to deploy the infrastructure to
    required: true
  backendKey:
    description: The key of the Terraform backend configuration
    required: true
  workingDirectory:
    description: The directory where the Terraform configuration is located
    required: true
  destroyPlanningMode:
    description: Creates a plan to destroy all objects currently managed
    default: "false"
    required: false
  environment:
    description: Environment to provision
    required: true

runs:
  using: composite
  steps:
    - name: 'Set subscription'
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        az account set --subscription ${{ inputs.subscriptionId }}

    - uses: hashicorp/setup-terraform@v3
      with:
        terraform_wrapper: false

    - name: 'Terraform > Init'
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        terraform init -backend-config="backend-${{ inputs.environment }}.conf"

    - name: 'Terraform > Workspace'
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        echo "Current environment: ${{ inputs.environment }}"
        terraform workspace new ${{ inputs.environment }}
        terraform workspace select ${{ inputs.environment }}

    - name: 'Terraform > Plan'
      id: plan
      if: ${{ inputs.destroyPlanningMode == 'false' }}
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        terraform plan -out="tfplan-aks-core"
        echo "PLAN=tfplan-${{ inputs.lzStage }}" >> $GITHUB_ENV

    - name: 'Terraform > Plan Destroy'
      id: plan-destroy
      if: ${{ inputs.destroyPlanningMode == 'true' }}
      shell: pwsh
      working-directory: ${{ inputs.workingDirectory }}
      run: |
        terraform plan -out="tfplan-aks-core" -destroy
        echo "PLAN=tfplan-${{ inputs.lzStage }}" >> $GITHUB_ENV

    - uses: actions/upload-artifact@v4
      with:
        name: "tfplan-aks-core-${{ github.run_id }}"
        path: "${{ inputs.workingDirectory }}/tfplan-aks-core"
        if-no-files-found: error
        retention-days: 5
        overwrite: true