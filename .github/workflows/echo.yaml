name: Echo

on:
  workflow_call:
    inputs:
      ENVIRONMENT:
        required: true
        type: string
      DRY_RUN:
        required: false
        type: boolean
        default: false

jobs:
  echo:
    runs-on: ubuntu-latest
    environment: 
      name: ${{ inputs.ENVIRONMENT }}
    steps:
      - name: Echo Environment Variables
        shell: bash
        run: |
          echo "vars.BASE_URL: ${{ vars.BASE_URL }}"
          echo "Dry Run Mode: ${{ inputs.DRY_RUN }}"

      - name: Check secret exists
        env:
          AZ_LZ_AKS_CREDENTIALS: ${{ secrets.AZ_LZ_AKS_CREDENTIALS }}
        if: ${{ env.AZ_LZ_AKS_CREDENTIALS == '' }}
        run: 'echo "AZ_LZ_AKS_CREDENTIALS does not exist"'

      - name: Check secret exists
        env:
          ARGOCD_OCI_TOKEN: ${{ secrets.ARGOCD_OCI_TOKEN }}
        if: ${{ env.ARGOCD_OCI_TOKEN == '' }}
        run: 'echo "ARGOCD_OCI_TOKEN does not exist"'

      - name: Azure Login
        if: ${{ inputs.DRY_RUN == false }}
        uses: azure/login@v2
        with:
          creds: ${{ secrets.AZ_LZ_AKS_CREDENTIALS }}

      - name: Azure Show Account
        if: ${{ inputs.DRY_RUN == false }}
        uses: azure/cli@v2
        with:
          azcliversion: 2.0.72
          inlineScript: |
            az account show
