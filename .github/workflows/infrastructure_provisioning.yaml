name: Infrastructure Provisioning
run-name: Provisioning environment ${{ inputs.environment }} using Terraform 🚀

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Specify the environment to run'
        required: true
        type: choice
        options:
          - dev
          - stage
          - prod
      dry_run:
        description: 'Dry run the deployment'
        required: false
        type: boolean
        default: true  # Ensure this is a string if necessary, remove quotes for boolean

  pull_request:
    types:
      - closed
    branches:
      - main

jobs:
  provision_dev_infra:
    if: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'dev' || github.event_name == 'pull_request' }}
    uses: ./.github/workflows/aks_deployment.yaml
    # uses: ./.github/workflows/echo.yaml
    with:
      ENVIRONMENT: dev
      DRY_RUN: ${{ github.event.inputs.dry_run == 'true' }}
    secrets: inherit

  provision_dev_bootstrap:
    if: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'dev' || github.event_name == 'pull_request' }}
    uses: ./.github/workflows/bootstrap_deployment.yaml
    # uses: ./.github/workflows/echo.yaml
    with:
      ENVIRONMENT: dev
      DRY_RUN: ${{ github.event.inputs.dry_run == 'true' }}
    secrets: inherit
    needs: provision_dev_infra

  provision_stage_infra:
    if: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'stage' || github.event_name == 'pull_request' }}
    uses: ./.github/workflows/aks_deployment.yaml
    with:
      ENVIRONMENT: stage
      DRY_RUN: ${{ github.event.inputs.dry_run == 'true' }}
    secrets: inherit

  provision_stage_bootstrap:
    if: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'stage' || github.event_name == 'pull_request' }}
    uses: ./.github/workflows/bootstrap_deployment.yaml
    with:
      ENVIRONMENT: stage
      DRY_RUN: ${{ github.event.inputs.dry_run == 'true' }}
    secrets: inherit
    needs: provision_stage_infra

  provision_prod_infra:
    if: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'prod' || github.event_name == 'pull_request' }}
    uses: ./.github/workflows/aks_deployment.yaml
    with:
      ENVIRONMENT: prod
      DRY_RUN: ${{ github.event.inputs.dry_run == 'true' }}
    secrets: inherit

  provision_prod_bootstrap:
    if: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'prod' || github.event_name == 'pull_request' }}
    uses: ./.github/workflows/bootstrap_deployment.yaml
    with:
      ENVIRONMENT: prod
      DRY_RUN: ${{ github.event.inputs.dry_run == 'true' }}
    secrets: inherit
    needs: provision_prod_infra
