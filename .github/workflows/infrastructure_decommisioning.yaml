name: Infrastructure Decommissioning
run-name: Decommissioning enviroment ${{ inputs.environment }} using Terraform 

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Specify the environment to run'
        required: true
        type: choice
        options:
          - dev
      dry_run:
        description: 'Dry run the deployment'
        required: false
        type: boolean
        default: true

jobs:
  aks-cluster-infra-validate:
    name: 'Validate AKS Cluster Infrastructure'
    runs-on: [ ubuntu-arc-payex ]
    environment: 
      name: ${{ inputs.ENVIRONMENT }}

    steps:
    - uses: actions/checkout@v4

    - name: 'Azure Login'
      uses: azure/login@v2
      with:
        creds: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}

    - name: Prepare environment variables
      id: aks-cluster-infra-validate-prepare-env-var
      shell: pwsh
      env:
        AZURE_CREDENTIALS: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}
      run: |
        # Parse Azure secret into Terraform variables
        $servicePrincipal = ($env:AZURE_CREDENTIALS | ConvertFrom-Json)
        $env:ARM_CLIENT_ID = $servicePrincipal.clientId
        $env:ARM_CLIENT_SECRET = $servicePrincipal.clientSecret
        $env:ARM_SUBSCRIPTION_ID = $servicePrincipal.subscriptionId
        $env:ARM_TENANT_ID = $servicePrincipal.tenantId

        # Save environment variable setup for subsequent steps
        Get-ChildItem -Path Env: -Recurse -Include ARM_*,TF_VAR_* | ForEach-Object {Write-Output "$($_.Name)=$($_.Value)"} >> $env:GITHUB_ENV

    - name: Validate AKS Cluster Infrastructure deployment
      id: validate-aks-cluster-infra-action
      uses: ./.github/workflows/actions/terraform-validate
      with:
        deploymentCredentials: "${{ secrets['AZ_LZ_ITOPS_CREDENTIALS'] }}"
        subscriptionId: "${{ env.ARM_SUBSCRIPTION_ID }}"
        workingDirectory: "./infrastructure"
        environment: "${{ inputs.ENVIRONMENT }}"
        destroyPlanningMode: "true"

  aks-cluster-infra-undeploy:
    name: 'Undeploy AKS Cluster Infrastructure'
    needs: aks-cluster-infra-validate
    if: ${{ inputs.dry_run == false }} # Only run if dry_run is false
    runs-on: [ ubuntu-arc-payex ]
    environment: "${{ inputs.ENVIRONMENT }}"

    steps:
    - uses: actions/checkout@v4

    - name: 'Azure Login'
      uses: azure/login@v2
      with:
        creds: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}
    
    - name: Prepare environment variables
      id: aks-cluster-infra-undeploy-prepare-env-var
      shell: pwsh
      env:
        AZURE_CREDENTIALS: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}
      run: |
        # Parse Azure secret into Terraform variables
        $servicePrincipal = ($env:AZURE_CREDENTIALS | ConvertFrom-Json)
        $env:ARM_CLIENT_ID = $servicePrincipal.clientId
        $env:ARM_CLIENT_SECRET = $servicePrincipal.clientSecret
        $env:ARM_SUBSCRIPTION_ID = $servicePrincipal.subscriptionId
        $env:ARM_TENANT_ID = $servicePrincipal.tenantId

        # Save environment variable setup for subsequent steps
        Get-ChildItem -Path Env: -Recurse -Include ARM_*,TF_VAR_* | ForEach-Object {Write-Output "$($_.Name)=$($_.Value)"} >> $env:GITHUB_ENV

    - name: Undeploy AKS Cluster Infrastructure
      id: undeploy-aks-cluster-infra-action
      uses: ./.github/workflows/actions/terraform-undeploy
      with:
        deploymentCredentials: "${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}"
        subscriptionId: "${{ env.ARM_SUBSCRIPTION_ID }}"
        workingDirectory: "./infrastructure"
        environment: "${{ inputs.ENVIRONMENT }}"

