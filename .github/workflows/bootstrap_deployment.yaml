name: Bootstrap - deploy

on:
  workflow_call:
    inputs:
      ENVIRONMENT:
        required: true
        type: string
      dry_run:
        required: false
        type: boolean
        default: false

jobs:

  aks-cluster-bootstrap-validate:
    name: 'Validate AKS Cluster Bootstrap'
    if: ${{ inputs.dry_run == false }} # Only run if dry_run is false
    runs-on: [ ubuntu-arc-payex ]
    environment: 
      name: ${{ inputs.ENVIRONMENT }}

    steps:
    - uses: actions/checkout@v4

    - name: 'Azure Login'
      uses: azure/login@v2
      with:
        creds: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}

    - name: Prepare bootstrap environment variables
      id: aks-cluster-bootstrap-validate-prepare-env-var
      shell: pwsh
      env:
        AZURE_CREDENTIALS: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}
        ARGOCD_GITHUB_TOKEN: ${{ secrets['ARGOCD_GITHUB_TOKEN'] }}
        ARGOCD_OCI_TOKEN: ${{ secrets['ARGOCD_OCI_TOKEN'] }}
      run: |
        $servicePrincipal = ($env:AZURE_CREDENTIALS | ConvertFrom-Json)
        $env:ARM_CLIENT_ID = $servicePrincipal.clientId
        $env:ARM_CLIENT_SECRET = $servicePrincipal.clientSecret
        $env:ARM_SUBSCRIPTION_ID = $servicePrincipal.subscriptionId
        $env:ARM_TENANT_ID = $servicePrincipal.tenantId
        $env:TF_VAR_helm_registry_password = $env:ARGOCD_OCI_TOKEN
        Get-ChildItem -Path Env: -Recurse -Include ARM_*,TF_VAR_* | ForEach-Object {Write-Output "$($_.Name)=$($_.Value)"} >> $env:GITHUB_ENV

    - name: Validate AKS Cluster Bootstrap deployment
      id: validate-aks-cluster-bootstrap-action
      uses: ./.github/workflows/actions/terraform-validate
      with:
        deploymentCredentials: "${{ secrets['AZ_LZ_ITOPS_CREDENTIALS'] }}"
        subscriptionId: "${{ env.ARM_SUBSCRIPTION_ID }}"
        environment: "${{ inputs.ENVIRONMENT }}"
        workingDirectory: "./bootstrap"

  aks-cluster-bootstrap-deploy:
    name: 'Deploy AKS Cluster Bootstrap'
    needs: aks-cluster-bootstrap-validate
    if: ${{ inputs.dry_run == false }} # Only run if dry_run is false
    runs-on: [ ubuntu-arc-payex ]
    environment: 
      name: ${{ inputs.ENVIRONMENT }}

    steps:
    - uses: actions/checkout@v4

    - name: 'Azure Login'
      uses: azure/login@v2
      with:
        creds: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}

    - name: Prepare environment variables
      id: aks-cluster-bootstrap-deploy-prepare-env-var
      shell: pwsh
      env:
        AZURE_CREDENTIALS: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}
      run: |
        $servicePrincipal = ($env:AZURE_CREDENTIALS | ConvertFrom-Json)
        $env:ARM_CLIENT_ID = $servicePrincipal.clientId
        $env:ARM_CLIENT_SECRET = $servicePrincipal.clientSecret
        $env:ARM_SUBSCRIPTION_ID = $servicePrincipal.subscriptionId
        $env:ARM_TENANT_ID = $servicePrincipal.tenantId
        Get-ChildItem -Path Env: -Recurse -Include ARM_*,TF_VAR_* | ForEach-Object {Write-Output "$($_.Name)=$($_.Value)"} >> $env:GITHUB_ENV

    - name: Deploy AKS Cluster Bootstrap
      id: deploy-aks-cluster-bootstrap-action
      uses: ./.github/workflows/actions/terraform-deploy
      with:
        deploymentCredentials: "${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}"
        subscriptionId: "${{ env.ARM_SUBSCRIPTION_ID }}"
        environment: "${{ inputs.ENVIRONMENT }}"
        workingDirectory: "./bootstrap"
        dryRun: ${{ inputs.DRY_RUN }}
