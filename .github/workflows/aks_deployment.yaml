name: AKS Cluster Infrastructure - deploy

on:
  workflow_call:
    inputs:
      ENVIRONMENT:
        required: true
        type: string
      dry_run:
        required: false
        type: boolean
        default: false

jobs:
  aks-cluster-infra-validate:
    name: 'Validate AKS Cluster Infrastructure'
    runs-on: [ ubuntu-arc-payex ]
    environment:
      name: ${{ inputs.ENVIRONMENT }}

    steps:
    - uses: actions/checkout@v4

    - name: 'Azure Login'
      uses: azure/login@v2
      with:
        creds: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}

    - name: Prepare environment variables
      id: aks-cluster-infra-validate-prepare-env-var
      shell: pwsh
      env:
        AZURE_CREDENTIALS: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}
      run: |
        # Parse Azure secret into Terraform variables
        $servicePrincipal = ($env:AZURE_CREDENTIALS | ConvertFrom-Json)
        $env:ARM_CLIENT_ID = $servicePrincipal.clientId
        $env:ARM_CLIENT_SECRET = $servicePrincipal.clientSecret
        $env:ARM_SUBSCRIPTION_ID = $servicePrincipal.subscriptionId
        $env:ARM_TENANT_ID = $servicePrincipal.tenantId

        # Save environment variable setup for subsequent steps
        Get-ChildItem -Path Env: -Recurse -Include ARM_*,TF_VAR_* | ForEach-Object {Write-Output "$($_.Name)=$($_.Value)"} >> $env:GITHUB_ENV

    - name: Validate AKS Cluster Infrastructure deployment
      id: validate-aks-cluster-infra-action
      uses: ./.github/workflows/actions/terraform-validate
      with:
        deploymentCredentials: "${{ secrets['AZ_LZ_ITOPS_CREDENTIALS'] }}"
        subscriptionId: "${{ env.ARM_SUBSCRIPTION_ID }}"
        workingDirectory: "./infrastructure"
        environment: "${{ inputs.ENVIRONMENT }}"

  aks-cluster-infra-deploy:
    name: 'Deploy AKS Cluster Infrastructure'
    needs: aks-cluster-infra-validate
    if: ${{ inputs.dry_run == false }} # Only run if dry_run is false
    runs-on: [ ubuntu-arc-payex ]
    environment:
      name: ${{ inputs.ENVIRONMENT }}

    steps:
    - uses: actions/checkout@v4

    - name: 'Azure Login'
      uses: azure/login@v2
      with:
        creds: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}

    - name: Prepare environment variables
      id: aks-cluster-infra-deploy-prepare-env-var
      shell: pwsh
      env:
        AZURE_CREDENTIALS: ${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}
        ARGOCD_GITHUB_TOKEN: ${{ secrets['ARGOCD_GITHUB_TOKEN'] }}
        ARGOCD_OCI_TOKEN: ${{ secrets['ARGOCD_OCI_TOKEN'] }}
      run: |
        # Parse Azure secret into Terraform variables
        $servicePrincipal = ($env:AZURE_CREDENTIALS | ConvertFrom-Json)
        $env:ARM_CLIENT_ID = $servicePrincipal.clientId
        $env:ARM_CLIENT_SECRET = $servicePrincipal.clientSecret
        $env:ARM_SUBSCRIPTION_ID = $servicePrincipal.subscriptionId
        $env:ARM_TENANT_ID = $servicePrincipal.tenantId

        # Set input variables
        $env:TF_VAR_git_repo_token = $env:ARGOCD_GITHUB_TOKEN
        $env:TF_VAR_helm_registry_password = $env:ARGOCD_OCI_TOKEN

        # Save environment variable setup for subsequent steps
        Get-ChildItem -Path Env: -Recurse -Include ARM_*,TF_VAR_* | ForEach-Object {Write-Output "$($_.Name)=$($_.Value)"} >> $env:GITHUB_ENV

    - name: Deploy AKS Cluster Infrastructure
      id: deploy-aks-cluster-infra-action
      uses: ./.github/workflows/actions/terraform-deploy
      with:
        deploymentCredentials: "${{ secrets['AZ_LZ_AKS_CREDENTIALS'] }}"
        subscriptionId: "${{ env.ARM_SUBSCRIPTION_ID }}"
        environment: "${{ inputs.ENVIRONMENT }}"
        workingDirectory: "./infrastructure"
        dryRun: ${{ inputs.dry_run }}
