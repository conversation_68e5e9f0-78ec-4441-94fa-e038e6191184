resource "kubernetes_namespace" "namespaces" {
  count = length(local.namespaces)
  metadata {
    name = local.namespaces[count.index]
  }
}

resource "helm_release" "argocd-secrets" {
  name       = "argocd-secrets"
  namespace  = "argocd"
  repository = "oci://ghcr.io/payex/helm-charts"
  chart      = "argocd-secrets"
  version    = "1.0.0"

  set {
    name  = "tenantId"
    value = data.azurerm_client_config.current.tenant_id
  }
  set {
    name  = "keyvaultName"
    value = data.azurerm_key_vault.keyvault.name
  }
  set {
    name  = "clientId"
    value = data.azurerm_user_assigned_identity.user_assigned_identity.client_id
  }
  depends_on = [kubernetes_namespace.namespaces]
}

resource "helm_release" "argocd" {
  name       = "argocd"
  namespace  = "argocd"
  repository = "https://argoproj.github.io/argo-helm"
  chart      = "argo-cd"
  version    = "7.4.3"
  values = [
    "${file("charts/argo-cd/values.yaml")}",
    "${file("charts/argo-cd/environments/${local.environment}/values.yaml")}"
  ]

  # Compute a hash of the values.yaml file to force an upgrade when the file changes
  set {
    name  = "values_hash"
    value = sha256(file("charts/argo-cd/values.yaml"))
  }

  set {
    name  = "values_env_hash"
    value = sha256(file("charts/argo-cd/environments/${local.environment}/values.yaml"))
  }

  # set {
  #   name  = "server.ingress.hostname"
  #   value = "argocd.${local.loadBalancerIP}.nip.io"
  # }
  set {
    name  = "configs.cm.ui\\.bannercontent"
    value = local.environment
  }
  depends_on = [helm_release.argocd-secrets]
}

resource "helm_release" "argocd-app-of-apps-parent" {
  name       = "argocd-app-of-apps-parent"
  namespace  = "argocd"
  repository = "oci://ghcr.io/payex/helm-charts"
  chart      = "argocd-app-of-apps-parent"
  version    = local.argo_app_of_apps_parent_version

  # Compute a hash of any values.yaml if used, and set any specific upgrade triggers here
  set {
    name  = "environment"
    value = local.environment
  }
  set {
    name  = "tenantId"
    value = data.azurerm_client_config.current.tenant_id
  }
  set {
    name  = "keyvaultName"
    value = data.azurerm_key_vault.keyvault.name
  }
  set {
    name  = "clientId"
    value = data.azurerm_user_assigned_identity.user_assigned_identity.client_id
  }
  set {
    name  = "loadBalancerIP"
    value = local.loadBalancerIP
  }
  depends_on = [helm_release.argocd]
}
