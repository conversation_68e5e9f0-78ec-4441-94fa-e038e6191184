variable "systemname" {
  type = map(string)
  default = {
    dev   = "cloud-data"
    stage = "cloud-data"
    prod  = "cloud-data"
  }
}

variable "existing_sharedsvcs_name_rg" {
  type = map(string)
  default = {
    dev   = "dev-cloud-data-sharedsvcs-rg"
    stage = "stage-cloud-data-sharedsvcs-rg"
    prod  = "prod-cloud-data-sharedsvcs-rg"
  }
}

variable "existing_key_vault_name" {
  type = map(string)
  default = {
    dev   = "dev-cloud-data-kv-63"
    stage = "stage-cloud-data-kv-15"
    prod  = "prod-cloud-data-kv-30"
  }
}

variable "helm_registry_url" {
  type = map(string)
  default = {
    dev   = "oci://ghcr.io/payex/helm-charts"
    stage = "oci://ghcr.io/payex/helm-charts"
    prod  = "oci://ghcr.io/payex/helm-charts"
  }
}
variable "helm_registry_username" {
  type = map(string)
  default = {
    dev   = "not-used"
    stage = "not-used"
    prod  = "not-used"
  }
}

variable "helm_registry_password" {
  default   = "token"
  type      = string
  sensitive = true
}

variable "loadBalancerIP" {
  type = map(string)
  default = {
    dev   = "*************"
    stage = "************"
    prod  = "*************"
  }
}
variable "namespaces" {
  type = map(list(string))
  default = {
    dev   = ["argocd", "insight", "kafka", "monitoring", "ingress-nginx", "automate", "cert-manager", "dp-kyc"]
    stage = ["argocd", "insight", "kafka", "monitoring", "ingress-nginx", "automate", "cert-manager", "dp-kyc"]
    prod  = ["argocd", "insight", "kafka", "monitoring", "ingress-nginx", "automate", "cert-manager"]
  }
}

variable "argo_app_of_apps_parent_version" {
  type = map(string)
  default = {
    dev   = "3.0.1"
    stage = "3.0.1"
    prod  = "3.0.1"
  }
}
