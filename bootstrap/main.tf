terraform {
  required_version = ">= 1.4.6"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }

    helm = {
      version = "2.15.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "2.23.0"
    }
  }

  backend "azurerm" {
  }
}

data "azurerm_client_config" "current" {}

data "azurerm_kubernetes_cluster" "credentials" {
  name                = local.aks_cluster_name
  resource_group_name = local.cluster_rg
}

provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
}

provider "kubernetes" {
  host                   = data.azurerm_kubernetes_cluster.credentials.kube_config[0].host
  client_certificate     = base64decode(data.azurerm_kubernetes_cluster.credentials.kube_config[0].client_certificate)
  client_key             = base64decode(data.azurerm_kubernetes_cluster.credentials.kube_config[0].client_key)
  cluster_ca_certificate = base64decode(data.azurerm_kubernetes_cluster.credentials.kube_config[0].cluster_ca_certificate)
}


provider "helm" {
  kubernetes {
    host                   = data.azurerm_kubernetes_cluster.credentials.kube_config[0].host
    client_certificate     = base64decode(data.azurerm_kubernetes_cluster.credentials.kube_config[0].client_certificate)
    client_key             = base64decode(data.azurerm_kubernetes_cluster.credentials.kube_config[0].client_key)
    cluster_ca_certificate = base64decode(data.azurerm_kubernetes_cluster.credentials.kube_config[0].cluster_ca_certificate)
  }
  registry {
    url      = local.helm_registry_url
    username = local.helm_registry_username
    password = var.helm_registry_password
  }
}

data "azurerm_key_vault" "keyvault" {
  name                = local.key_vault_name
  resource_group_name = local.key_vault_rg
}
data "azurerm_user_assigned_identity" "user_assigned_identity" {
  name                = local.user_assigned_identity_name
  resource_group_name = local.user_assigned_identity_rg
}

