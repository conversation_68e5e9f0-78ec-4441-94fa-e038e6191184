locals {
  environment                     = terraform.workspace
  systemname                      = lookup(var.systemname, terraform.workspace, null)
  aks_cluster_name                = "${local.environment}-${local.systemname}-aks"
  cluster_rg                      = "${local.environment}-${local.systemname}-aks-rg"
  key_vault_rg                    = lookup(var.existing_sharedsvcs_name_rg, terraform.workspace, null)
  key_vault_name                  = lookup(var.existing_key_vault_name, terraform.workspace, null)
  user_assigned_identity_name     = "${local.environment}-${local.systemname}-uai"
  user_assigned_identity_rg       = lookup(var.existing_sharedsvcs_name_rg, terraform.workspace, null)
  helm_registry_username          = lookup(var.helm_registry_username, terraform.workspace, null)
  helm_registry_url               = lookup(var.helm_registry_url, terraform.workspace, null)
  namespaces                      = lookup(var.namespaces, terraform.workspace, null)
  loadBalancerIP                  = lookup(var.loadBalancerIP, terraform.workspace, null)
  argo_app_of_apps_parent_version = lookup(var.argo_app_of_apps_parent_version, terraform.workspace, null)
}
