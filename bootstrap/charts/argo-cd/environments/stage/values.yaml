server:
  ingress:
    annotations:
      cert-manager.io/cluster-issuer: "dataplatform-cluster-issuer"
      cert-manager.io/common-name: "argocd.kafka.stage.payex.net"
      cert-manager.io/alt-name: "argocd.kafka.stage.payex.net"
      cert-manager.io/revision-history-limit: "1"
      external-dns.alpha.kubernetes.io/hostname: "argocd.kafka.stage.payex.net"
    hostname: argocd.kafka.stage.payex.net
