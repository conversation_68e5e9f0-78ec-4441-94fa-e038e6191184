global:
  logging:
    format: "json"
  affinity:
    podAntiAffinity: soft
    nodeAffinity:
      type: hard
      matchExpressions:
        - key: agentpool
          operator: In
          values:
            - general
            - generaltemp
configs:
  params:
    server.insecure: false
  cm:
    application.resourceTrackingMethod: annotation
    ui.bannercontent: env
    ui.bannerpermanent: "true"
    ui.bannerposition: bottom
server:
  replicas: 1
  ingress:
    enabled: true
    annotations:
      nginx.ingress.kubernetes.io/force-ssl-redirect: true
      nginx.ingress.kubernetes.io/ssl-passthrough: true
      nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    ingressClassName: nginx
    hostname: argocd.127.0.0.1.nip.io
    tls: true
  config:
    repositories: |
      - type: helm
        name: stable
        url: https://charts.helm.sh/stable
repoServer:
  serviceAccount:
    create: true
    name: "workload-identity-sa"
    annotations:
      azure.workload.identity/client-id: ""  # This will be set via Terraform
    labels:
      azure.workload.identity/use: "true"
  volumeMounts:
    - name: secrets-store
      mountPath: "/secrets-store"
      readOnly: true
  volumes:
    - name: secrets-store
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: "argocd-secret-provider"
