{"version": 4, "terraform_version": "1.4.6", "serial": 51, "lineage": "1227a601-2636-3329-7d6b-47698d9b9e2f", "outputs": {}, "resources": [{"mode": "data", "type": "azurerm_kubernetes_cluster", "name": "credentials", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"aci_connector_linux": [], "agent_pool_profile": [{"count": 3, "enable_auto_scaling": false, "enable_node_public_ip": false, "max_count": 0, "max_pods": 30, "min_count": 0, "name": "nodepool", "node_labels": {}, "node_public_ip_prefix_id": "", "node_taints": [], "orchestrator_version": "1.25.6", "os_disk_size_gb": 128, "os_type": "Linux", "tags": {}, "type": "VirtualMachineScaleSets", "upgrade_settings": [], "vm_size": "Standard_D2_v2", "vnet_subnet_id": "/subscriptions/0741761e-85cd-4e27-9182-81f6041e1862/resourceGroups/dev-kafka-01-vnet-rg/providers/Microsoft.Network/virtualNetworks/dev-kafka-01-vnet-01-18/subnets/nodepool", "zones": []}], "api_server_authorized_ip_ranges": [], "azure_active_directory_role_based_access_control": [], "azure_policy_enabled": true, "disk_encryption_set_id": "/subscriptions/0741761e-85cd-4e27-9182-81f6041e1862/resourceGroups/dev-kafka-01-sharedsvcs-rg/providers/Microsoft.Compute/diskEncryptionSets/aks-diskencryption", "dns_prefix": "dev-kafka-01-aks", "fqdn": "", "http_application_routing_enabled": false, "http_application_routing_zone_name": "", "id": "/subscriptions/0741761e-85cd-4e27-9182-81f6041e1862/resourceGroups/dev-kafka-01-aks-rg/providers/Microsoft.ContainerService/managedClusters/dev-kafka-01-aks", "identity": [], "ingress_application_gateway": [], "key_management_service": [], "key_vault_secrets_provider": [{"secret_identity": [], "secret_rotation_enabled": true, "secret_rotation_interval": "2m"}], "kube_admin_config": [], "kube_admin_config_raw": "", "kube_config": [{"client_certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUZIakNDQXdhZ0F3SUJBZ0lSQUw1TjFoRE9CREpKVFRlME5mTUlMWDB3RFFZSktvWklodmNOQVFFTEJRQXcKRFRFTE1Ba0dBMVVFQXhNQ1kyRXdIaGNOTWpNd05UTXhNRGd4TlRJNFdoY05NalV3TlRNeE1EZ3lOVEk0V2pBdwpNUmN3RlFZRFZRUUtFdzV6ZVhOMFpXMDZiV0Z6ZEdWeWN6RVZNQk1HQTFVRUF4TU1iV0Z6ZEdWeVkyeHBaVzUwCk1JSUNJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBZzhBTUlJQ0NnS0NBZ0VBdDdiVGZTbS9adm1RWE0ySlZnYVAKakN5S2RoanE5Ulh5S1RXUm1vWHNZMTM4L1pNaTh5bnNhbmQvcDMyTHBkSEg5bWdQdVMxRHNESnRaV3owQzljQQpCV0lCbjJRaE5wc1l0SVozNW5HMS9rbmo5Y1p2MGM0bWkvQWcwM1dVZU5mMW9mYUdlQzJmeWlvWUprTlhEcEdTCjFQeWNnS01uVXMweG1PbE82MDNOcTR4YlRISzhLVEE2VUh3NGJETmcrcVZTSDhQdFMyMEp0UytFSHJpdi9VNzcKY2IrZjJBZ2RTY0FQUENoYkxJS09scnkzcytCL3B5bmhxV3VTb1draHBRbElLczVjRFRGdTFpMW1rakJMWU8waApDVjFNdTZRUWZOeGFLV2xSR3JTakNJbi9ud3VtWUNnL2dadk1qRHlISk5PaExnUVFKUzVOWlI5MHpSdFVRc2VGCnc0N3dFS3JEdWZOQUc4WStIWWdia0U1TlcyUk1MbmY5NHNaRUtzSWVmc0tTd1NiZlkzVENpbEoxV3FOUXoxMWEKMyt2UUdFOXkzSk8wQkduRjZqcVRzNFBoVTJNRFBGMTJyQVNveXFsYkt2UE5wbEtRK1dwOHcxOFFvUUhKTktRMQpyUk9BU0JFNU81eFhvY3RKbFArWUpxcjRwakFYUnl3WHluT3l0RDYyem9Vc29zNUp6NjBiOU5HUTFDRTdXQ000ClViZ1dFajRINzViT21FZHdBNUtLcUZmcklOK2FTYTYzQWtXMHpmSTZPWk41aEdERUVFSFQwVUNjNDdnMFcwUjgKK0h4R1dzTlEzQkxFbTAxbVlLc2dYK0xsajBVbHlBOG9UWEtqUnVXRHJFR0Q5elVleUdNdGwzejhrYnBzbWY0WQpMSkVHdy9UY2RiOU00eXdiYm0waU1UOENBd0VBQWFOV01GUXdEZ1lEVlIwUEFRSC9CQVFEQWdXZ01CTUdBMVVkCkpRUU1NQW9HQ0NzR0FRVUZCd01DTUF3R0ExVWRFd0VCL3dRQ01BQXdId1lEVlIwakJCZ3dGb0FVMGVsclREN0wKdVFtZHZnQnhnbThMZG00UTJlb3dEUVlKS29aSWh2Y05BUUVMQlFBRGdnSUJBS2s0YVVlZ1crcWZsQk1yekEzaQp6dHlxUVoyQzFkY1ozSU9QZ01zdjhsSFlQZmlTMWgvYVN6VUFrWGFvR3A3ZTZnaVpqUnpFWldSYlhQWXNXY1ZMCnhFRDI5SVJXT3pqbFNPbTU2WnRoNWUyWTdwdFVyTkdNdUdkS25DSVZLZmx2citNeUIrOVRweWErQitZRU1RcWwKNDJFdGpjZzByNnR0Q01ROFlja0lobmV2cFdZVlpJRDUvNTdQQTNmdVBnNUcvVkNXTk9mU2VTekorRmdVdE50ZQp4WC81aE9uUzBSN3pYZWdmTU14Vkw2UHJaR0pHMlNXYVZ6Zm43ZFFXVnNTMDJIdWw3WURtTzcrSW04bjhQdm9OCjJDT0ppQW55RXJ0UUNLRyt0SmVSWGpSN1F2cXI1MW9xMWZjZEVqbDRNRUJvMnB4ck1IOEQvYW1zY3Y0OGJYTlgKcng1cjE5M2c0Z3JCa2diNWtvWXJDOVBwZ3ZwK083b1ZpaWRxaUZSbFV3b2hIRnlPcUhGUWEzTDBjY3RIclVhQwowMCtrZ1VpeXRDSVhPU2ZJWVhBc0lxWmxwc1g1YXRBc2JuNVIrblBNWWhyNzB2SU5oQnROQ0duTDRuMWs3d2pKCmRSZkR3QXljTStTOUhQMGo4NlNqc0tQVGFlMnEzZlZKNStjNHM4eWF2N2lwRmdrZ1J4QXV1bjQzd29TWkRDYWwKL0ZYK2xmMnZ2Y05ES2F4VUpZdmJjdnlyd1RiRzdUaVZIaUwrNzMvdktuZGszdHgrMHM1QkxDaDkxVHhXV1pQMQpHbVRCOHVYRXEzSWk4SzV1VkVjOU5mZ0xmQlJycmZxMDZJbXFybWRJaHI4MmR6cTd3ZlpwL1ZJcGpmSndSOHB4CnA4MW9GMWNOU3B5UHhzWnlnaGs1S2NsWQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==", "client_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "cluster_ca_certificate": "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", "host": "https://dev-kafka-01-aks-49w5iab7.privatelink.swedencentral.azmk8s.io:443", "password": "4axowjqxn93ou4eaml6u3wx8rfu9baiikgnpouvnxuv4c1qp2cu5vhemqy4jwgeukyuemd0fipbxjpgcyp0a5w0ogt9ug5d0h5402hzyi3ttknnt5i0s0abcpszpbs6u", "username": "clusterUser_dev-kafka-01-aks-rg_dev-kafka-01-aks"}], "kube_config_raw": "apiVersion: v1\nclusters:\n- cluster:\n    certificate-authority-data: 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\n    server: https://dev-kafka-01-aks-49w5iab7.privatelink.swedencentral.azmk8s.io:443\n  name: dev-kafka-01-aks\ncontexts:\n- context:\n    cluster: dev-kafka-01-aks\n    user: clusterUser_dev-kafka-01-aks-rg_dev-kafka-01-aks\n  name: dev-kafka-01-aks\ncurrent-context: dev-kafka-01-aks\nkind: Config\npreferences: {}\nusers:\n- name: clusterUser_dev-kafka-01-aks-rg_dev-kafka-01-aks\n  user:\n    client-certificate-data: 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\n    client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n    token: 4axowjqxn93ou4eaml6u3wx8rfu9baiikgnpouvnxuv4c1qp2cu5vhemqy4jwgeukyuemd0fipbxjpgcyp0a5w0ogt9ug5d0h5402hzyi3ttknnt5i0s0abcpszpbs6u\n", "kubelet_identity": [], "kubernetes_version": "1.25.6", "linux_profile": [{"admin_username": "", "ssh_key": []}], "location": "swedencentral", "microsoft_defender": [], "name": "dev-kafka-01-aks", "network_profile": [{"dns_service_ip": "***********", "docker_bridge_cidr": "**********/16", "load_balancer_sku": "Standard", "network_plugin": "azure", "network_policy": "", "pod_cidr": "", "service_cidr": "***********/16"}], "node_resource_group": "dev-kafka-01-aks-nodepool-rg", "node_resource_group_id": "/subscriptions/0741761e-85cd-4e27-9182-81f6041e1862/resourceGroups/dev-kafka-01-aks-nodepool-rg", "oidc_issuer_enabled": true, "oidc_issuer_url": "https://swedencentral.oic.prod-aks.azure.com/d357e4b0-a5d5-400f-b185-86fb574bef72/08df3ec8-106c-41c1-bff1-afe0e8cf4d44/", "oms_agent": [], "open_service_mesh_enabled": false, "private_cluster_enabled": true, "private_fqdn": "dev-kafka-01-aks-49w5iab7.privatelink.swedencentral.azmk8s.io", "resource_group_name": "dev-kafka-01-aks-rg", "role_based_access_control_enabled": true, "service_principal": [{"client_id": "ea5b459f-0ad6-413f-86c0-0f3a1cd1e210"}], "storage_profile": [{"blob_driver_enabled": false, "disk_driver_enabled": true, "disk_driver_version": "v1", "file_driver_enabled": true, "snapshot_controller_enabled": true}], "tags": {"costcenter": "12510 SP IT", "systemcode": "default"}, "timeouts": null, "windows_profile": [{"admin_username": "azureuser"}]}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "kubernetes_namespace", "name": "namespaces", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "argocd", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "argocd", "resource_version": "3775576", "uid": "06fb6add-fa7c-4413-bd89-01a1a68022a9"}], "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************", "dependencies": ["data.azurerm_kubernetes_cluster.credentials"]}, {"index_key": 2, "schema_version": 0, "attributes": {"id": "kafka", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "kafka", "resource_version": "3775993", "uid": "83df3ea6-20f4-4646-a0f4-23a0f47cc74d"}], "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************", "dependencies": ["data.azurerm_kubernetes_cluster.credentials"]}]}], "check_results": null}