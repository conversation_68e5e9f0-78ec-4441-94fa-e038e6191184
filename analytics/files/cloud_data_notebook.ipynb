{"cells": [{"cell_type": "markdown", "id": "579622d4-31cf-498e-8875-53b0a507098f", "metadata": {"microsoft": {"language": "python", "language_group": "synapse_pyspark"}, "nteract": {"transient": {"deleting": false}}}, "source": ["* Print Delta Lake schema\n", "* Count all authorizations\n", "* Count all authorizations by date\n", "* Sum amount1 by merchant and month\n", "* Delete and Vacuum\n"]}, {"cell_type": "code", "execution_count": 1, "id": "4ec6eace-cea9-4c2b-9e2e-5e8193bba8f7", "metadata": {"editable": true, "microsoft": {"language": "python", "language_group": "synapse_pyspark"}, "run_control": {"frozen": false}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:15:14.9371101Z", "execution_start_time": "2025-09-10T07:14:35.9733997Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "bf2fe01e-552f-497c-961a-f0398e3e12f2", "queued_time": "2025-09-10T07:09:00.8415644Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": "2025-09-10T07:09:00.8426422Z", "spark_pool": null, "state": "finished", "statement_id": 3, "statement_ids": [3]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 3, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- authorizationInfo: struct (nullable = true)\n", " |    |-- account: string (nullable = true)\n", " |    |-- acquiringInstitutionId: string (nullable = true)\n", " |    |-- acquiringInterchangeSettlementDate: date (nullable = true)\n", " |    |-- address: string (nullable = true)\n", " |    |-- addressVerificationStatus: string (nullable = true)\n", " |    |-- adjustmentsImpactSettlement: boolean (nullable = false)\n", " |    |-- amount1: double (nullable = true)\n", " |    |-- amount2: double (nullable = true)\n", " |    |-- approvalCode: string (nullable = true)\n", " |    |-- approvalCodeLength: string (nullable = true)\n", " |    |-- authorizationIndicator: string (nullable = true)\n", " |    |-- authorizationIndicator2: string (nullable = true)\n", " |    |-- authorizationType: string (nullable = true)\n", " |    |-- authorizer: string (nullable = true)\n", " |    |-- batchSequenceNumber: string (nullable = true)\n", " |    |-- branchId: string (nullable = true)\n", " |    |-- cardAcceptanceIdNumber: string (nullable = true)\n", " |    |-- cardIssuingIdNumber: string (nullable = true)\n", " |    |-- cardType: string (nullable = true)\n", " |    |-- certificateAuthorizationGroup: string (nullable = true)\n", " |    |-- certificateAuthorizedUserId: string (nullable = true)\n", " |    |-- clerkId: string (nullable = true)\n", " |    |-- destination: string (nullable = true)\n", " |    |-- draftCaptureFlag: string (nullable = true)\n", " |    |-- entryTime: timestamp (nullable = true)\n", " |    |-- exceptionReasonCode: string (nullable = true)\n", " |    |-- exitTime: timestamp (nullable = true)\n", " |    |-- expirationDate: string (nullable = true)\n", " |    |-- forwardingInstitutionIdNumber: string (nullable = true)\n", " |    |-- interchangeResponse: string (nullable = true)\n", " |    |-- invoiceNumber: string (nullable = true)\n", " |    |-- issuerCode: string (nullable = true)\n", " |    |-- issuingInterchangeSettlementDate: date (nullable = true)\n", " |    |-- multiCurrencyAuthConversionRate: string (nullable = true)\n", " |    |-- multiCurrencyAuthCurrencyCode: string (nullable = true)\n", " |    |-- multiCurrencyConversionDateTime: timestamp (nullable = true)\n", " |    |-- multiCurrencySettlementConversionRate: string (nullable = true)\n", " |    |-- multiCurrencySettlementCurrencyCode: string (nullable = true)\n", " |    |-- numberOfChargebacks: integer (nullable = true)\n", " |    |-- original: string (nullable = true)\n", " |    |-- originalBase24PostingDate: string (nullable = true)\n", " |    |-- originalCurrencyCode: string (nullable = true)\n", " |    |-- originalInvoiceNumber: string (nullable = true)\n", " |    |-- originalMessageType: string (nullable = true)\n", " |    |-- originalSequenceNumber: string (nullable = true)\n", " |    |-- originalTransactionDate: string (nullable = true)\n", " |    |-- originalTransactionTime: string (nullable = true)\n", " |    |-- originator: string (nullable = true)\n", " |    |-- overrideFlag: string (nullable = true)\n", " |    |-- pinIndicator: string (nullable = true)\n", " |    |-- pinOffset: string (nullable = true)\n", " |    |-- pinTries: string (nullable = true)\n", " |    |-- pointOfServiceConditionCode: string (nullable = true)\n", " |    |-- pointOfServiceEntryMode: string (nullable = true)\n", " |    |-- postingDate: date (nullable = true)\n", " |    |-- preAuthorizationHoldLevel: string (nullable = true)\n", " |    |-- preAuthorizationSequenceNumber: string (nullable = true)\n", " |    |-- preAuthorizationTimestampDate: string (nullable = true)\n", " |    |-- preAuthorizationTimestampTime: string (nullable = true)\n", " |    |-- pseudoTerminalId: string (nullable = true)\n", " |    |-- reEntryTime: timestamp (nullable = true)\n", " |    |-- reasonForChargeback: string (nullable = true)\n", " |    |-- receivingInstitutionId: string (nullable = true)\n", " |    |-- referralPhone: string (nullable = true)\n", " |    |-- refreshAvailableBalance: string (nullable = true)\n", " |    |-- refreshCRBalance: string (nullable = true)\n", " |    |-- refreshCRLimit: string (nullable = true)\n", " |    |-- refreshCurrencyFloatBalance: string (nullable = true)\n", " |    |-- refreshImpactIndicator: boolean (nullable = false)\n", " |    |-- refreshIndicatorPBF1: string (nullable = true)\n", " |    |-- refreshIndicatorPBF2: string (nullable = true)\n", " |    |-- refreshIndicatorPBF3: string (nullable = true)\n", " |    |-- refreshIndicatorPBF4: string (nullable = true)\n", " |    |-- refreshTTLFloatBalance: string (nullable = true)\n", " |    |-- responder: string (nullable = true)\n", " |    |-- responseCode: string (nullable = true)\n", " |    |-- retailSICCode: string (nullable = true)\n", " |    |-- reversalCode: string (nullable = true)\n", " |    |-- routeStatus: string (nullable = true)\n", " |    |-- sequenceNumber: string (nullable = true)\n", " |    |-- settlementFlag: string (nullable = true)\n", " |    |-- shiftNumber: string (nullable = true)\n", " |    |-- terminalCity: string (nullable = true)\n", " |    |-- terminalCountryCode: string (nullable = true)\n", " |    |-- terminalNameLocation: string (nullable = true)\n", " |    |-- terminalOwnerName: string (nullable = true)\n", " |    |-- terminalState: string (nullable = true)\n", " |    |-- terminalTimeOffset: string (nullable = true)\n", " |    |-- terminalType: string (nullable = true)\n", " |    |-- track2: string (nullable = true)\n", " |    |-- transactionCode: string (nullable = true)\n", " |    |-- transactionCodeAccountAssociation: string (nullable = true)\n", " |    |-- transactionCodeCategory: string (nullable = true)\n", " |    |-- transactionCodeType: string (nullable = true)\n", " |    |-- transactionDate: date (nullable = true)\n", " |    |-- transactionTime: string (nullable = true)\n", " |    |-- userField2: string (nullable = true)\n", " |    |-- userField4: string (nullable = true)\n", " |    |-- userField5: string (nullable = true)\n", " |    |-- zipCode: string (nullable = true)\n", " |-- cardInfo: struct (nullable = true)\n", " |    |-- cardIssuerFinancialInstitution: string (nullable = true)\n", " |    |-- cardIssuerLogicalNetwork: string (nullable = true)\n", " |    |-- cardMemberNumber: string (nullable = true)\n", " |    |-- cardNumber: string (nullable = true)\n", " |-- dataFlag: struct (nullable = true)\n", " |    |-- dataFlag: string (nullable = true)\n", " |-- retailerInfo: struct (nullable = true)\n", " |    |-- retailerKeyFinancialInstitution: string (nullable = true)\n", " |    |-- retailerKeyGroup: string (nullable = true)\n", " |    |-- retailerKeyId: string (nullable = true)\n", " |    |-- retailerKeyRegion: string (nullable = true)\n", " |    |-- retailerKyLogicalNetwork: string (nullable = true)\n", " |    |-- retailerTerminalBatchNumber: string (nullable = true)\n", " |    |-- retailerTerminalId: string (nullable = true)\n", " |    |-- retailerTerminalShiftNumber: string (nullable = true)\n", " |-- terminalInfo: struct (nullable = true)\n", " |    |-- keyClerkId: string (nullable = true)\n", " |    |-- keyRecordFormat: string (nullable = true)\n", " |    |-- keyRetailerId: string (nullable = true)\n", " |    |-- terminalFinancialId: string (nullable = true)\n", " |    |-- terminalId: string (nullable = true)\n", " |    |-- terminalKeyId: string (nullable = true)\n", " |    |-- terminalLogicalNetwork: string (nullable = true)\n", " |    |-- terminalTime: string (nullable = true)\n", " |-- tokens: array (nullable = true)\n", " |    |-- element: struct (containsNull = false)\n", " |    |    |-- attributes: map (nullable = true)\n", " |    |    |    |-- key: string\n", " |    |    |    |-- value: string (valueContainsNull = true)\n", " |    |    |-- type: string (nullable = true)\n", " |-- transactionDetails: struct (nullable = true)\n", " |    |-- recordType: string (nullable = true)\n", " |    |-- transactionDateTime: timestamp (nullable = true)\n", " |-- merchantId: string (nullable = true)\n", " |-- transactionDate: date (nullable = true)\n", " |-- transactionDateMonth: string (nullable = true)\n", "\n"]}], "source": ["\n", "df = spark.table(\"authorizations\") \n", "\n", "# Skriv ut schemat\n", "df.printSchema()"]}, {"cell_type": "code", "execution_count": 12, "id": "104c4ca2-76a8-4138-b4bd-c6d685a91805", "metadata": {"collapsed": false, "microsoft": {"language": "sparksql", "language_group": "synapse_pyspark"}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:23:32.2308658Z", "execution_start_time": "2025-09-10T07:23:24.4133302Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "13134085-1536-41bb-9d5c-f7afeb72ddf2", "queued_time": "2025-09-10T07:23:24.4122275Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": null, "spark_pool": null, "state": "finished", "statement_id": 14, "statement_ids": [14]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 14, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.synapse.sparksql-result+json": {"data": [["11665"]], "schema": {"fields": [{"metadata": {}, "name": "number_of_authorizations", "nullable": false, "type": "long"}], "type": "struct"}}, "text/plain": ["<Spark SQL result set with 1 rows and 1 fields>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["%%sql\n", "\n", "SELECT\n", "    COUNT(*) AS number_of_authorizations\n", "FROM\n", "    authorizations\n"]}, {"cell_type": "code", "execution_count": 10, "id": "caa0c117-5eea-4010-ad2d-76c5fc586b61", "metadata": {"collapsed": false, "microsoft": {"language": "sparksql", "language_group": "synapse_pyspark"}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:22:09.1910779Z", "execution_start_time": "2025-09-10T07:21:55.3226687Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "9f5637c3-54dd-4ff1-bec4-5c333616e9b0", "queued_time": "2025-09-10T07:21:55.3215047Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": null, "spark_pool": null, "state": "finished", "statement_id": 12, "statement_ids": [12]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 12, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.synapse.sparksql-result+json": {"data": [["2025-12-18", "2"], ["2025-12-12", "16"], ["2025-12-04", "1"], ["2025-09-09", "3"], ["2025-09-08", "5497"], ["2025-09-07", "760"], ["2025-09-06", "759"], ["2025-09-05", "836"], ["2025-09-04", "650"], ["2025-09-03", "3139"], [null, "2"]], "schema": {"fields": [{"metadata": {}, "name": "authorization_date", "nullable": true, "type": "date"}, {"metadata": {}, "name": "number_of_authorizations", "nullable": false, "type": "long"}], "type": "struct"}}, "text/plain": ["<Spark SQL result set with 11 rows and 2 fields>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["%%sql\n", "\n", "SELECT\n", "    authorizationInfo.transactionDate AS authorization_date,\n", "    COUNT(*) AS number_of_authorizations\n", "FROM\n", "    authorizations\n", "GROUP BY\n", "    authorizationInfo.transactionDate\n", "ORDER BY\n", "    authorization_date DESC\n", "LIMIT 100;"]}, {"cell_type": "code", "execution_count": 4, "id": "cd213384-1db7-4f9c-b6ca-9377736f121d", "metadata": {"collapsed": false, "microsoft": {"language": "sparksql", "language_group": "synapse_pyspark"}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:16:22.715823Z", "execution_start_time": "2025-09-10T07:16:04.4580169Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "0cbec5b6-38b9-4bb7-aea3-796a002af2c9", "queued_time": "2025-09-10T07:09:01.083314Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": null, "spark_pool": null, "state": "finished", "statement_id": 6, "statement_ids": [6]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 6, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.synapse.sparksql-result+json": {"data": [["2788495", "2025-09", 20233388, "8639"], ["0010595", "2025-09", 17924235, "164"], ["0940403", "2025-09", 7520600, "301"], ["0862524", "2025-09", 7089700, "192"], ["2753341", "2025-09", 6963587, "6323"], ["9063652", "2025-09", 4172200, "534"], ["0392308", "2025-09", 4055300, "47"], ["9998618", "2025-09", 3040400, "168"], ["1406271", "2025-09", 2751613, "32"], ["9745050", "2025-09", 2435233, "55"], ["8395576", "2025-09", 2111213, "60"], ["9803628", "2025-09", 1853762, "18"], ["9749250", "2025-09", 1657886, "14"], ["9622259", "2025-09", 1588072, "16"], ["2672566", "2025-09", 1510900, "10"], ["9755455", "2025-09", 1507794, "497"], ["0907972", "2025-09", 1432676, "29"], ["1738194", "2025-09", 1385987, "24"], ["8620452", "2025-09", 1233488, "13"], ["8337776", "2025-09", 968448, "22"], ["8887606", "2025-09", 943008, "44"], ["9738030", "2025-09", 806000, "41"], ["0095372", "2025-09", 660674, "17"], ["9609082", "2025-09", 628260, "16"], ["0134890", "2025-09", 576320, "17"], ["2754737", "2025-09", 547060, "27"], ["9772070", "2025-09", 452718, "26"], ["9435181", "2025-09", 451700, "23"], ["1537299", "2025-09", 437805, "43"], ["2816700", "2025-09", 421700, "4"], ["2686665", "2025-09", 418400, "13"], ["0010587", "2025-09", 381900, "15"], ["9934316", "2025-09", 347020, "19"], ["9968850", "2025-09", 330315, "62"], ["2962637", "2025-09", 320000, "2"], ["9400409", "2025-09", 286000, "47"], ["9544685", "2025-09", 252830, "27"], ["9652835", "2025-09", 205300, "21"], ["1599265", "2025-09", 182575, "35"], ["0438291", "2025-09", 180000, "1"], ["2009561", "2025-09", 160205, "31"], ["8700528", "2025-09", 154068, "82"], ["0932814", "2025-09", 135875, "21"], ["2479012", "2025-09", 124015, "17"], ["2860096", "2025-09", 115923, "31"], ["9423906", "2025-09", 107803, "32"], ["0371732", "2025-09", 106235, "18"], ["0738476", "2025-09", 99200, "43"], ["1203058", "2025-09", 98368, "26"], ["2538759", "2025-09", 82000, "2"], ["9791229", "2025-09", 80484, "26"], ["2754604", "2025-09", 77184, "27"], ["9011800", "2025-09", 50198, "42"], ["8900953", "2025-09", 30707, "3"], ["0742338", "2025-09", 24200, "18"], ["1700004", "2025-09", 20000, "2"], ["0762286", "2025-09", 19900, "1"], ["2985620", "2025-09", 15374, "2"], ["077775500000000", "2025-09", 15110, "2"], ["0000010595", "2025-09", 15110, "2"], ["9667189", "2025-09", 10831, "16"], ["0131318", "2025-09", 10800, "19"], ["8207821", "2025-09", 8210, "12"], ["6202311", "2025-09", 7464, "13"], ["1195916", "2025-09", 4600, "5"], ["9675851", "2025-09", 4000, "2"], ["1111111", "2025-09", 3000, "3"], ["2527836", "2025-09", 2900, "1"], ["9171836", "2025-09", 1100, "1"], ["9761826", "2025-09", 200, "1"], ["9652835", "2025-08", 9357300, "36"], ["2788495", "2025-08", 5654163, "2420"], ["0940403", "2025-08", 4566500, "181"], ["2686665", "2025-08", 3660400, "77"], ["0862524", "2025-08", 1186500, "40"], ["9749250", "2025-08", 1065087, "8"], ["0392308", "2025-08", 852000, "5"], ["2753341", "2025-08", 841844, "830"], ["8620452", "2025-08", 823694, "11"], ["0762286", "2025-08", 810400, "17"], ["9622259", "2025-08", 508802, "5"], ["8395576", "2025-08", 394031, "14"], ["1599265", "2025-08", 378843, "9"], ["8337776", "2025-08", 280332, "11"], ["9738030", "2025-08", 264000, "14"], ["9609082", "2025-08", 240925, "8"], ["9063652", "2025-08", 226500, "47"], ["9400409", "2025-08", 199300, "32"], ["1406271", "2025-08", 164484, "2"], ["1203058", "2025-08", 106436, "46"], ["9745050", "2025-08", 102440, "36"], ["2009561", "2025-08", 90041, "11"], ["1537299", "2025-08", 67000, "7"], ["2754604", "2025-08", 52580, "22"], ["0738476", "2025-08", 47100, "20"], ["9544685", "2025-08", 24699, "9"], ["9423906", "2025-08", 19121, "5"], ["9755455", "2025-08", 18294, "63"], ["0095372", "2025-08", 11700, "1"], ["6202311", "2025-08", 10826, "7"]], "schema": {"fields": [{"metadata": {}, "name": "merchantId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "transactionDateMonth", "nullable": true, "type": "string"}, {"metadata": {}, "name": "total_amount", "nullable": true, "type": "double"}, {"metadata": {}, "name": "number_of_authorizations", "nullable": false, "type": "long"}], "type": "struct"}}, "text/plain": ["<Spark SQL result set with 100 rows and 4 fields>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["%%sql\n", "\n", "SELECT\n", "    merchantId,\n", "    transactionDateMonth,\n", "    SUM(authorizationInfo.amount1) AS total_amount,\n", "    COUNT(*) as number_of_authorizations\n", "FROM\n", "    authorizations\n", "WHERE\n", "    merchantId IS NOT NULL \n", "    AND transactionDateMonth IS NOT NULL\n", "GROUP BY\n", "    merchantId,\n", "    transactionDateMonth\n", "ORDER BY\n", "    transactionDateMonth DESC,\n", "    total_amount DESC\n", "LIMIT 100;"]}, {"cell_type": "code", "execution_count": 5, "id": "7125d033-179a-459c-85be-869f33126fca", "metadata": {"collapsed": false, "microsoft": {"language": "sparksql", "language_group": "synapse_pyspark"}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:16:53.7738638Z", "execution_start_time": "2025-09-10T07:16:22.7179571Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "53c2fd4e-de32-4279-921b-1cd112c11340", "queued_time": "2025-09-10T07:09:01.1996903Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": null, "spark_pool": null, "state": "finished", "statement_id": 7, "statement_ids": [7]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 7, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.synapse.sparksql-result+json": {"data": [["10513"]], "schema": {"fields": [{"metadata": {}, "name": "num_affected_rows", "nullable": true, "type": "long"}], "type": "struct"}}, "text/plain": ["<Spark SQL result set with 1 rows and 1 fields>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["%%sql\n", "\n", "DELETE FROM authorizations\n", "WHERE authorizationInfo.transactionDate < date_sub(current_date(), 7)"]}, {"cell_type": "code", "execution_count": 6, "id": "feb0054c-6937-4b2e-9d61-49f831dfe2ed", "metadata": {"advisor": {"adviceMetadata": "{\"artifactId\":\"64483730-9026-446f-9bd6-a9e99b18f14a\",\"activityId\":\"ad56ae77-f5fd-41d4-8343-b57005b6cbea\",\"applicationId\":\"application_1757488290105_0001\",\"jobGroupId\":\"8\",\"advices\":{\"warn\":1}}"}, "collapsed": false, "microsoft": {"language": "sparksql", "language_group": "synapse_pyspark"}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:18:48.2524007Z", "execution_start_time": "2025-09-10T07:16:53.7761242Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "1180f526-ee47-44c2-bd44-3aa108a46976", "queued_time": "2025-09-10T07:09:01.2196008Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": null, "spark_pool": null, "state": "finished", "statement_id": 8, "statement_ids": [8]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 8, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.synapse.sparksql-result+json": {"data": [["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=6826010"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=1154285"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=6860258"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=9829037"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=836"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=2886166"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=1181205"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=2753342"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=8168619"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=9898990"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=0011171"], ["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations/transactionDateMonth=2025-09/merchantId=0935643"]], "schema": {"fields": [{"metadata": {}, "name": "path", "nullable": true, "type": "string"}], "type": "struct"}}, "text/plain": ["<Spark SQL result set with 12 rows and 1 fields>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["%%sql\n", "\n", "VACUUM authorizations RETAIN 168 HOURS DRY RUN\n"]}, {"cell_type": "code", "execution_count": 7, "id": "c6a9ddd5-4324-4a8d-96b0-db307ef0092a", "metadata": {"collapsed": false, "microsoft": {"language": "sparksql", "language_group": "synapse_pyspark"}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:18:56.0469783Z", "execution_start_time": "2025-09-10T07:18:48.2547727Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "5942de91-d18d-4a60-85d9-d36af8cf680b", "queued_time": "2025-09-10T07:09:01.2649739Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": null, "spark_pool": null, "state": "finished", "statement_id": 9, "statement_ids": [9]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 9, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.synapse.sparksql-result+json": {"data": [["abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations", {"schema": [{"dataType": {}, "metadata": {"map": {}}, "name": "numFilesAdded", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "numFilesRemoved", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "numFilesUpdatedWithoutRewrite", "nullable": false}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "metadata": {"map": {}}, "name": "filesAdded", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "metadata": {"map": {}}, "name": "filesRemoved", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "metadata": {"map": {}}, "name": "filesUpdatedWithoutRewrite", "nullable": true}, {"dataType": {"containsNull": true, "elementType": [{"dataType": {}, "metadata": {"map": {}}, "name": "reason", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "metadata": {"map": {}}, "name": "metrics", "nullable": true}]}, "metadata": {"map": {}}, "name": "filesRemovedBreakdown", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "partitionsOptimized", "nullable": false}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "strategyName", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "num", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "size", "nullable": false}], "metadata": {"map": {}}, "name": "inputCubeFiles", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "num", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "size", "nullable": false}], "metadata": {"map": {}}, "name": "inputOtherFiles", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "inputNumCubes", "nullable": false}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "num", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "size", "nullable": false}], "metadata": {"map": {}}, "name": "mergedFiles", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "numOutputCubes", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "mergedNumCubes", "nullable": true}], "metadata": {"map": {}}, "name": "zOrderStats", "nullable": true}, {"dataType": [{"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "numFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "size", "nullable": false}], "metadata": {"map": {}}, "name": "inputZCubeFiles", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "numFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "size", "nullable": false}], "metadata": {"map": {}}, "name": "inputOtherFiles", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "inputNumZCubes", "nullable": false}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "numFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "size", "nullable": false}], "metadata": {"map": {}}, "name": "mergedFiles", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "numOutputZCubes", "nullable": false}], "metadata": {"map": {}}, "name": "clusteringStats", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "numBatches", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalConsideredFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFilesSkipped", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "preserveInsertionOrder", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "numFilesSkippedToReduceWriteAmplification", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "numBytesSkippedToReduceWriteAmplification", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "startTimeMs", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "endTimeMs", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalClusterParallelism", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalScheduledTasks", "nullable": false}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "maxClusterActiveParallelism", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "minClusterActiveParallelism", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "maxSessionActiveParallelism", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "minSessionActiveParallelism", "nullable": true}], "metadata": {"map": {}}, "name": "autoCompactParallelismStats", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "numDeletionVectorsRemoved", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "numDeletionVectorRowsRemoved", "nullable": false}], "metadata": {"map": {}}, "name": "deletionVectorStats", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "numTableColumns", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "numTableColumnsWithStats", "nullable": false}], "values": ["1", "6", "0", {"schema": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "values": ["70615", "70615", 70615, "1", "70615"]}, {"schema": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "values": ["32480", "69405", 39774, "6", "238644"]}, {"schema": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "values": [null, null, 0, "0", "0"]}, [{"schema": [{"dataType": {}, "metadata": {"map": {}}, "name": "reason", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "metadata": {"map": {}}, "name": "metrics", "nullable": true}], "values": ["NonVOrderedFiles", {"schema": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "values": ["32480", "69405", 39774, "6", "238644"]}]}, {"schema": [{"dataType": {}, "metadata": {"map": {}}, "name": "reason", "nullable": true}, {"dataType": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "metadata": {"map": {}}, "name": "metrics", "nullable": true}], "values": ["SmallFiles", {"schema": [{"dataType": {}, "metadata": {"map": {}}, "name": "min", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "max", "nullable": true}, {"dataType": {}, "metadata": {"map": {}}, "name": "avg", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalFiles", "nullable": false}, {"dataType": {}, "metadata": {"map": {}}, "name": "totalSize", "nullable": false}], "values": ["32480", "69405", 39774, "6", "238644"]}]}], "1", null, null, "1", "6", "0", false, "0", "0", "1757488734016", "0", "8", "0", null, null, "10", "10"]}]], "schema": {"fields": [{"metadata": {}, "name": "path", "nullable": true, "type": "string"}, {"metadata": {}, "name": "metrics", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "numFilesAdded", "nullable": false, "type": "long"}, {"metadata": {}, "name": "numFilesRemoved", "nullable": false, "type": "long"}, {"metadata": {}, "name": "numFilesUpdatedWithoutRewrite", "nullable": false, "type": "long"}, {"metadata": {}, "name": "filesAdded", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "min", "nullable": true, "type": "long"}, {"metadata": {}, "name": "max", "nullable": true, "type": "long"}, {"metadata": {}, "name": "avg", "nullable": false, "type": "double"}, {"metadata": {}, "name": "totalFiles", "nullable": false, "type": "long"}, {"metadata": {}, "name": "totalSize", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "filesRemoved", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "min", "nullable": true, "type": "long"}, {"metadata": {}, "name": "max", "nullable": true, "type": "long"}, {"metadata": {}, "name": "avg", "nullable": false, "type": "double"}, {"metadata": {}, "name": "totalFiles", "nullable": false, "type": "long"}, {"metadata": {}, "name": "totalSize", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "filesUpdatedWithoutRewrite", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "min", "nullable": true, "type": "long"}, {"metadata": {}, "name": "max", "nullable": true, "type": "long"}, {"metadata": {}, "name": "avg", "nullable": false, "type": "double"}, {"metadata": {}, "name": "totalFiles", "nullable": false, "type": "long"}, {"metadata": {}, "name": "totalSize", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "filesRemovedBreakdown", "nullable": true, "type": {"containsNull": true, "elementType": {"fields": [{"metadata": {}, "name": "reason", "nullable": true, "type": "string"}, {"metadata": {}, "name": "metrics", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "min", "nullable": true, "type": "long"}, {"metadata": {}, "name": "max", "nullable": true, "type": "long"}, {"metadata": {}, "name": "avg", "nullable": false, "type": "double"}, {"metadata": {}, "name": "totalFiles", "nullable": false, "type": "long"}, {"metadata": {}, "name": "totalSize", "nullable": false, "type": "long"}], "type": "struct"}}], "type": "struct"}, "type": "array"}}, {"metadata": {}, "name": "partitionsOptimized", "nullable": false, "type": "long"}, {"metadata": {}, "name": "zOrderStats", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "strategyName", "nullable": true, "type": "string"}, {"metadata": {}, "name": "inputCubeFiles", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "num", "nullable": false, "type": "long"}, {"metadata": {}, "name": "size", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "inputOtherFiles", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "num", "nullable": false, "type": "long"}, {"metadata": {}, "name": "size", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "inputNumCubes", "nullable": false, "type": "long"}, {"metadata": {}, "name": "mergedFiles", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "num", "nullable": false, "type": "long"}, {"metadata": {}, "name": "size", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "numOutputCubes", "nullable": false, "type": "long"}, {"metadata": {}, "name": "mergedNumCubes", "nullable": true, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "clusteringStats", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "inputZCubeFiles", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "numFiles", "nullable": false, "type": "long"}, {"metadata": {}, "name": "size", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "inputOtherFiles", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "numFiles", "nullable": false, "type": "long"}, {"metadata": {}, "name": "size", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "inputNumZCubes", "nullable": false, "type": "long"}, {"metadata": {}, "name": "mergedFiles", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "numFiles", "nullable": false, "type": "long"}, {"metadata": {}, "name": "size", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "numOutputZCubes", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "numBatches", "nullable": false, "type": "long"}, {"metadata": {}, "name": "totalConsideredFiles", "nullable": false, "type": "long"}, {"metadata": {}, "name": "totalFilesSkipped", "nullable": false, "type": "long"}, {"metadata": {}, "name": "preserveInsertionOrder", "nullable": false, "type": "boolean"}, {"metadata": {}, "name": "numFilesSkippedToReduceWriteAmplification", "nullable": false, "type": "long"}, {"metadata": {}, "name": "numBytesSkippedToReduceWriteAmplification", "nullable": false, "type": "long"}, {"metadata": {}, "name": "startTimeMs", "nullable": false, "type": "long"}, {"metadata": {}, "name": "endTimeMs", "nullable": false, "type": "long"}, {"metadata": {}, "name": "totalClusterParallelism", "nullable": false, "type": "long"}, {"metadata": {}, "name": "totalScheduledTasks", "nullable": false, "type": "long"}, {"metadata": {}, "name": "autoCompactParallelismStats", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "maxClusterActiveParallelism", "nullable": true, "type": "long"}, {"metadata": {}, "name": "minClusterActiveParallelism", "nullable": true, "type": "long"}, {"metadata": {}, "name": "maxSessionActiveParallelism", "nullable": true, "type": "long"}, {"metadata": {}, "name": "minSessionActiveParallelism", "nullable": true, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "deletionVectorStats", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "numDeletionVectorsRemoved", "nullable": false, "type": "long"}, {"metadata": {}, "name": "numDeletionVectorRowsRemoved", "nullable": false, "type": "long"}], "type": "struct"}}, {"metadata": {}, "name": "numTableColumns", "nullable": false, "type": "long"}, {"metadata": {}, "name": "numTableColumnsWithStats", "nullable": false, "type": "long"}], "type": "struct"}}], "type": "struct"}}, "text/plain": ["<Spark SQL result set with 1 rows and 2 fields>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["%%sql\n", "\n", "OPTIMIZE authorizations\n", "WHERE transactionDateMonth = '2025-09' AND merchantId = '9998618'"]}, {"cell_type": "code", "execution_count": 8, "id": "81813ae8-f038-4ca5-8cf5-91398fe19fce", "metadata": {"collapsed": false, "microsoft": {"language": "sparksql", "language_group": "synapse_pyspark"}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:18:59.394242Z", "execution_start_time": "2025-09-10T07:18:56.049259Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "7bf427b5-389e-4d0c-973f-447b193e52b5", "queued_time": "2025-09-10T07:09:01.32155Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": null, "spark_pool": null, "state": "finished", "statement_id": 10, "statement_ids": [10]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 10, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.synapse.sparksql-result+json": {"data": [["delta", "67b57eb3-cf11-4c71-9e49-db5c45dd086c", "spark_catalog.chimcobldhq2asrkc5jmanr3dhnnap2vchgn8oavetnn4qrje1gm6p95edq62pr5bthmorrlchfm8obkc5fmoobbclk6utbjckim8ojf.authorizations", null, "abfss://<EMAIL>/a9dd9d49-c756-43b0-8bef-5318fe95a0cc/Tables/dbo/authorizations", "2025-08-30T18:09:26Z", "2025-09-10T07:18:53Z", ["transactionDateMonth", "merchantId"], [], "307", "11879623", {"delta.parquet.vorder.enabled": "true"}, 1, 2, ["appendOnly", "invariants"]]], "schema": {"fields": [{"metadata": {}, "name": "format", "nullable": true, "type": "string"}, {"metadata": {}, "name": "id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "name", "nullable": true, "type": "string"}, {"metadata": {}, "name": "description", "nullable": true, "type": "string"}, {"metadata": {}, "name": "location", "nullable": true, "type": "string"}, {"metadata": {}, "name": "createdAt", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "lastModified", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "partitionColumns", "nullable": true, "type": {"containsNull": true, "elementType": "string", "type": "array"}}, {"metadata": {}, "name": "clusteringColumns", "nullable": true, "type": {"containsNull": true, "elementType": "string", "type": "array"}}, {"metadata": {}, "name": "numFiles", "nullable": true, "type": "long"}, {"metadata": {}, "name": "sizeInBytes", "nullable": true, "type": "long"}, {"metadata": {}, "name": "properties", "nullable": true, "type": {"keyType": "string", "type": "map", "valueContainsNull": true, "valueType": "string"}}, {"metadata": {}, "name": "minReaderVersion", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "minWriterVersion", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "tableFeatures", "nullable": true, "type": {"containsNull": true, "elementType": "string", "type": "array"}}], "type": "struct"}}, "text/plain": ["<Spark SQL result set with 1 rows and 15 fields>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["%%sql\n", "DESCRIBE DETAIL authorizations"]}, {"cell_type": "code", "execution_count": 9, "id": "b96dfe86-6697-4aaf-8e59-d4c71a88f774", "metadata": {"collapsed": false, "microsoft": {"language": "sparksql", "language_group": "synapse_pyspark"}}, "outputs": [{"data": {"application/vnd.livy.statement-meta+json": {"execution_finish_time": "2025-09-10T07:19:02.779533Z", "execution_start_time": "2025-09-10T07:18:59.3963843Z", "livy_statement_state": "available", "normalized_state": "finished", "parent_msg_id": "a582d471-4f6d-45f0-864d-fde9185ba051", "queued_time": "2025-09-10T07:09:01.3624815Z", "session_id": "ad56ae77-f5fd-41d4-8343-b57005b6cbea", "session_start_time": null, "spark_pool": null, "state": "finished", "statement_id": 11, "statement_ids": [11]}, "text/plain": ["StatementMeta(, ad56ae77-f5fd-41d4-8343-b57005b6cbea, 11, Finished, Available, Finished)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.synapse.sparksql-result+json": {"data": [["112", "2025-09-10T07:18:53Z", null, null, "OPTIMIZE", {"auto": "false", "clusterBy": "[]", "predicate": "[\"(('transactionDateMonth = 2025-09) AND ('merchantId = 9998618))\"]", "zOrderBy": "[]"}, null, null, null, "111", "SnapshotIsolation", false, {"maxFileSize": "70615", "minFileSize": "70615", "numAddedBytes": "70615", "numAddedFiles": "1", "numDeletionVectorsRemoved": "0", "numRemovedBytes": "238644", "numRemovedFiles": "6", "p25FileSize": "70615", "p50FileSize": "70615", "p75FileSize": "70615"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250905.3"], ["111", "2025-09-10T07:16:51Z", null, null, "DELETE", {"predicate": "[\"(authorizationInfo#883.transactionDate < 2025-09-03)\"]"}, null, null, null, "110", "Serializable", false, {"executionTimeMs": "27283", "numAddedBytes": "501976", "numAddedChangeFiles": "0", "numAddedFiles": "7", "numCopiedRows": "348", "numDeletedRows": "10513", "numDeletionVectorsAdded": "0", "numDeletionVectorsRemoved": "0", "numDeletionVectorsUpdated": "0", "numRemovedBytes": "10926104", "numRemovedFiles": "279", "rewriteTimeMs": "12711", "scanTimeMs": "14569"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250905.3"], ["110", "2025-09-09T01:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "109", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "63402", "numOutputRows": "358", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["109", "2025-09-09T00:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "108", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "45799", "numOutputRows": "181", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["108", "2025-09-08T23:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "107", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "134883", "numOutputRows": "362", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["107", "2025-09-08T22:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "106", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "150699", "numOutputRows": "1345", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["106", "2025-09-08T21:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "105", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "128746", "numOutputRows": "359", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["105", "2025-09-08T20:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "104", "Serializable", true, {"numAddedFiles": "12", "numOutputBytes": "509555", "numOutputRows": "734", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["104", "2025-09-08T19:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "103", "Serializable", true, {"numAddedFiles": "5", "numOutputBytes": "273370", "numOutputRows": "1570", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["103", "2025-09-08T18:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "102", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "35387", "numOutputRows": "2", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["102", "2025-09-08T17:49:06Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "101", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "64728", "numOutputRows": "2", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["101", "2025-09-08T16:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "100", "Serializable", true, {"numAddedFiles": "5", "numOutputBytes": "180845", "numOutputRows": "45", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["100", "2025-09-08T15:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "99", "Serializable", true, {"numAddedFiles": "5", "numOutputBytes": "176375", "numOutputRows": "26", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["99", "2025-09-08T14:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "98", "Serializable", true, {"numAddedFiles": "18", "numOutputBytes": "634543", "numOutputRows": "93", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["98", "2025-09-08T13:59:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "97", "Serializable", true, {"numAddedFiles": "14", "numOutputBytes": "506167", "numOutputRows": "108", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["97", "2025-09-08T12:49:06Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "96", "Serializable", true, {"numAddedFiles": "14", "numOutputBytes": "481638", "numOutputRows": "49", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["96", "2025-09-08T11:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "95", "Serializable", true, {"numAddedFiles": "16", "numOutputBytes": "564656", "numOutputRows": "85", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["95", "2025-09-08T10:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "94", "Serializable", true, {"numAddedFiles": "10", "numOutputBytes": "353058", "numOutputRows": "70", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["94", "2025-09-08T09:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "93", "Serializable", true, {"numAddedFiles": "14", "numOutputBytes": "485563", "numOutputRows": "40", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["93", "2025-09-08T08:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "92", "Serializable", true, {"numAddedFiles": "11", "numOutputBytes": "380440", "numOutputRows": "34", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["92", "2025-09-08T07:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "91", "Serializable", true, {"numAddedFiles": "6", "numOutputBytes": "211028", "numOutputRows": "20", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["91", "2025-09-08T06:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "90", "Serializable", true, {"numAddedFiles": "8", "numOutputBytes": "276784", "numOutputRows": "19", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["90", "2025-09-08T05:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "89", "Serializable", true, {"numAddedFiles": "4", "numOutputBytes": "135758", "numOutputRows": "8", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["89", "2025-09-07T21:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "88", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "51455", "numOutputRows": "207", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["88", "2025-09-07T20:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "87", "Serializable", true, {"numAddedFiles": "4", "numOutputBytes": "176299", "numOutputRows": "324", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["87", "2025-09-07T19:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "86", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "43088", "numOutputRows": "178", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["86", "2025-09-07T17:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "85", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "35526", "numOutputRows": "4", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["85", "2025-09-07T14:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "84", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "38534", "numOutputRows": "30", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["84", "2025-09-07T13:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "83", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "36767", "numOutputRows": "15", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["83", "2025-09-07T07:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "82", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "33053", "numOutputRows": "1", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["82", "2025-09-07T03:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "81", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "33158", "numOutputRows": "1", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["81", "2025-09-06T21:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "80", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "79725", "numOutputRows": "104", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["80", "2025-09-06T20:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "79", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "144165", "numOutputRows": "411", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["79", "2025-09-06T19:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "78", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "43106", "numOutputRows": "178", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["78", "2025-09-06T17:49:05Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "77", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "35490", "numOutputRows": "4", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["77", "2025-09-06T16:49:06Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "76", "Serializable", true, {"numAddedFiles": "46", "numOutputBytes": "1814909", "numOutputRows": "1999", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["76", "2025-09-03T16:29:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "75", "Serializable", true, {"numAddedFiles": "4", "numOutputBytes": "143695", "numOutputRows": "37", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["75", "2025-09-03T14:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "74", "Serializable", true, {"numAddedFiles": "14", "numOutputBytes": "485315", "numOutputRows": "59", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["74", "2025-09-03T14:03:54Z", null, null, "OPTIMIZE", {"auto": "false", "clusterBy": "[]", "predicate": "[\"(('transactionDateMonth = 2025-09) AND ('merchantId = 9998618))\"]", "zOrderBy": "[]"}, null, null, null, "73", "SnapshotIsolation", false, {"maxFileSize": "77632", "minFileSize": "77632", "numAddedBytes": "77632", "numAddedFiles": "1", "numDeletionVectorsRemoved": "0", "numRemovedBytes": "697325", "numRemovedFiles": "20", "p25FileSize": "77632", "p50FileSize": "77632", "p75FileSize": "77632"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250731.1"], ["73", "2025-09-03T13:59:13Z", null, null, "VACUUM END", {"status": "COMPLETED"}, null, null, null, "72", "SnapshotIsolation", true, {"numDeletedFiles": "0", "numVacuumedDirectories": "104"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250731.1"], ["72", "2025-09-03T13:59:11Z", null, null, "VACUUM START", {"defaultRetentionMillis": "604800000", "retentionCheckEnabled": "true"}, null, null, null, "71", "SnapshotIsolation", true, {"numFilesToDelete": "0", "sizeOfDataToDelete": "0"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250731.1"], ["71", "2025-09-03T13:56:57Z", null, null, "DELETE", {"predicate": "[\"(authorizationInfo#3556.transactionDate < 2025-08-28)\"]"}, null, null, null, "70", "Serializable", false, {"executionTimeMs": "8983", "numAddedBytes": "0", "numAddedChangeFiles": "0", "numAddedFiles": "0", "numCopiedRows": "0", "numDeletedRows": "2", "numDeletionVectorsAdded": "0", "numDeletionVectorsRemoved": "0", "numDeletionVectorsUpdated": "0", "numRemovedBytes": "34859", "numRemovedFiles": "1", "rewriteTimeMs": "443", "scanTimeMs": "8539"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250731.1"], ["70", "2025-09-03T13:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "69", "Serializable", true, {"numAddedFiles": "11", "numOutputBytes": "380294", "numOutputRows": "47", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["69", "2025-09-03T13:48:51Z", null, null, "VACUUM END", {"status": "COMPLETED"}, null, null, null, "68", "SnapshotIsolation", true, {"numDeletedFiles": "0", "numVacuumedDirectories": "104"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250731.1"], ["68", "2025-09-03T13:48:50Z", null, null, "VACUUM START", {"defaultRetentionMillis": "604800000", "retentionCheckEnabled": "true"}, null, null, null, "67", "SnapshotIsolation", true, {"numFilesToDelete": "0", "sizeOfDataToDelete": "0"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250731.1"], ["67", "2025-09-03T13:48:15Z", null, null, "AUTOSET VORDER TBLPROPERTY", {}, null, null, null, "66", "Serializable", true, {}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250731.1"], ["66", "2025-09-03T13:48:14Z", null, null, "DELETE", {"predicate": "[\"(authorizationInfo#823.transactionDate < 2025-08-27)\"]"}, null, null, null, "65", "Serializable", false, {"executionTimeMs": "14855", "numAddedBytes": "684543", "numAddedChangeFiles": "0", "numAddedFiles": "7", "numCopiedRows": "3419", "numDeletedRows": "51", "numDeletionVectorsAdded": "0", "numDeletionVectorsRemoved": "0", "numDeletionVectorsUpdated": "0", "numRemovedBytes": "1101836", "numRemovedFiles": "25", "rewriteTimeMs": "5056", "scanTimeMs": "9798"}, null, "Apache-Spark/3.5.1.5.4.20250711.1 Delta-Lake/3.2.0.20250731.1"], ["65", "2025-09-03T12:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "64", "Serializable", true, {"numAddedFiles": "11", "numOutputBytes": "399057", "numOutputRows": "212", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["64", "2025-09-03T11:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "63", "Serializable", true, {"numAddedFiles": "10", "numOutputBytes": "356790", "numOutputRows": "53", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["63", "2025-09-03T10:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "62", "Serializable", true, {"numAddedFiles": "14", "numOutputBytes": "535642", "numOutputRows": "449", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["62", "2025-09-03T09:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "61", "Serializable", true, {"numAddedFiles": "8", "numOutputBytes": "276247", "numOutputRows": "18", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["61", "2025-09-03T08:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "60", "Serializable", true, {"numAddedFiles": "10", "numOutputBytes": "355199", "numOutputRows": "203", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["60", "2025-09-03T07:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "59", "Serializable", true, {"numAddedFiles": "11", "numOutputBytes": "402393", "numOutputRows": "311", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["59", "2025-09-03T06:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "58", "Serializable", true, {"numAddedFiles": "8", "numOutputBytes": "357963", "numOutputRows": "1225", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["58", "2025-09-03T05:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "57", "Serializable", true, {"numAddedFiles": "6", "numOutputBytes": "229652", "numOutputRows": "256", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["57", "2025-09-03T04:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "56", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "35735", "numOutputRows": "3", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["56", "2025-09-03T03:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "55", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "118575", "numOutputRows": "152", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["55", "2025-09-02T21:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "54", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "35931", "numOutputRows": "4", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["54", "2025-09-02T20:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "53", "Serializable", true, {"numAddedFiles": "11", "numOutputBytes": "451464", "numOutputRows": "436", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["53", "2025-09-02T19:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "52", "Serializable", true, {"numAddedFiles": "4", "numOutputBytes": "155541", "numOutputRows": "199", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["52", "2025-09-02T18:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "51", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "103369", "numOutputRows": "6", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["51", "2025-09-02T17:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "50", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "103542", "numOutputRows": "6", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["50", "2025-09-02T16:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "49", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "35430", "numOutputRows": "2", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["49", "2025-09-02T15:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "48", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "105272", "numOutputRows": "8", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["48", "2025-09-02T14:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "47", "Serializable", true, {"numAddedFiles": "4", "numOutputBytes": "140601", "numOutputRows": "45", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["47", "2025-09-02T13:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "46", "Serializable", true, {"numAddedFiles": "9", "numOutputBytes": "325925", "numOutputRows": "124", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["46", "2025-09-02T12:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "45", "Serializable", true, {"numAddedFiles": "12", "numOutputBytes": "449120", "numOutputRows": "499", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["45", "2025-09-02T11:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "44", "Serializable", true, {"numAddedFiles": "12", "numOutputBytes": "409111", "numOutputRows": "21", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["44", "2025-09-02T10:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "43", "Serializable", true, {"numAddedFiles": "6", "numOutputBytes": "205512", "numOutputRows": "24", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["43", "2025-09-02T09:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "42", "Serializable", true, {"numAddedFiles": "7", "numOutputBytes": "240778", "numOutputRows": "15", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["42", "2025-09-02T08:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "41", "Serializable", true, {"numAddedFiles": "9", "numOutputBytes": "310235", "numOutputRows": "17", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["41", "2025-09-02T07:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "40", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "65542", "numOutputRows": "2", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["40", "2025-09-02T06:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "39", "Serializable", true, {"numAddedFiles": "5", "numOutputBytes": "173375", "numOutputRows": "14", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["39", "2025-09-02T05:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "38", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "70635", "numOutputRows": "3", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["38", "2025-09-02T04:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "37", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "35606", "numOutputRows": "3", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["37", "2025-09-02T03:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "36", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "70650", "numOutputRows": "8", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["36", "2025-09-02T02:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "35", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "57658", "numOutputRows": "357", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["35", "2025-09-02T00:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "34", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "77369", "numOutputRows": "182", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["34", "2025-09-01T23:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "33", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "43362", "numOutputRows": "123", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["33", "2025-09-01T22:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "32", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "74483", "numOutputRows": "83", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["32", "2025-09-01T21:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "31", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "94253", "numOutputRows": "297", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["31", "2025-09-01T20:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "30", "Serializable", true, {"numAddedFiles": "10", "numOutputBytes": "424612", "numOutputRows": "403", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["30", "2025-09-01T19:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "29", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "103514", "numOutputRows": "762", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["29", "2025-09-01T18:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "28", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "35636", "numOutputRows": "2", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["28", "2025-09-01T15:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "27", "Serializable", true, {"numAddedFiles": "4", "numOutputBytes": "143644", "numOutputRows": "12", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["27", "2025-09-01T14:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "26", "Serializable", true, {"numAddedFiles": "14", "numOutputBytes": "500404", "numOutputRows": "147", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["26", "2025-09-01T13:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "25", "Serializable", true, {"numAddedFiles": "12", "numOutputBytes": "455125", "numOutputRows": "532", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["25", "2025-09-01T12:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "24", "Serializable", true, {"numAddedFiles": "12", "numOutputBytes": "445886", "numOutputRows": "332", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["24", "2025-09-01T11:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "23", "Serializable", true, {"numAddedFiles": "14", "numOutputBytes": "509916", "numOutputRows": "321", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["23", "2025-09-01T10:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "22", "Serializable", true, {"numAddedFiles": "10", "numOutputBytes": "372252", "numOutputRows": "235", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["22", "2025-09-01T09:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "21", "Serializable", true, {"numAddedFiles": "9", "numOutputBytes": "315747", "numOutputRows": "39", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["21", "2025-09-01T08:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "20", "Serializable", true, {"numAddedFiles": "9", "numOutputBytes": "338889", "numOutputRows": "371", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["20", "2025-09-01T07:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "19", "Serializable", true, {"numAddedFiles": "11", "numOutputBytes": "415666", "numOutputRows": "300", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["19", "2025-09-01T06:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "18", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "107689", "numOutputRows": "19", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["18", "2025-09-01T05:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "17", "Serializable", true, {"numAddedFiles": "3", "numOutputBytes": "110996", "numOutputRows": "78", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["17", "2025-09-01T04:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "16", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "70797", "numOutputRows": "4", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["16", "2025-09-01T03:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "15", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "78085", "numOutputRows": "70", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["15", "2025-09-01T02:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "14", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "38043", "numOutputRows": "22", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["14", "2025-08-31T23:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "13", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "39707", "numOutputRows": "46", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["13", "2025-08-31T22:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "12", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "72574", "numOutputRows": "43", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["12", "2025-08-31T21:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "11", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "70085", "numOutputRows": "3", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["11", "2025-08-31T20:49:32Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "10", "Serializable", true, {"numAddedFiles": "5", "numOutputBytes": "204210", "numOutputRows": "147", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["10", "2025-08-31T17:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "9", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "34630", "numOutputRows": "2", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["9", "2025-08-31T15:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "8", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "41091", "numOutputRows": "142", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["8", "2025-08-31T09:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "7", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "34773", "numOutputRows": "2", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["7", "2025-08-31T07:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "6", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "38917", "numOutputRows": "70", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["6", "2025-08-31T04:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "5", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "74941", "numOutputRows": "46", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["5", "2025-08-31T03:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "4", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "76952", "numOutputRows": "54", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["4", "2025-08-30T23:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "3", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "39984", "numOutputRows": "48", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["3", "2025-08-30T22:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "2", "Serializable", true, {"numAddedFiles": "1", "numOutputBytes": "39384", "numOutputRows": "41", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["2", "2025-08-30T21:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "1", "Serializable", true, {"numAddedFiles": "2", "numOutputBytes": "73479", "numOutputRows": "6", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["1", "2025-08-30T20:49:27Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, "0", "Serializable", true, {"numAddedFiles": "6", "numOutputBytes": "235186", "numOutputRows": "92", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"], ["0", "2025-08-30T18:09:28Z", null, null, "STREAMING UPDATE", {"mode": "Append", "partitionBy": "[\"transactionDateMonth\",\"merchantId\"]"}, null, null, null, null, "Serializable", true, {"numAddedFiles": "40", "numOutputBytes": "1730791", "numOutputRows": "3371", "numRemovedFiles": "0"}, null, "flink-engine/1.16.1-flink-delta-connector/3.2.0 Delta-Standalone/3.2.0"]], "schema": {"fields": [{"metadata": {}, "name": "version", "nullable": true, "type": "long"}, {"metadata": {}, "name": "timestamp", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "userId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "userName", "nullable": true, "type": "string"}, {"metadata": {}, "name": "operation", "nullable": true, "type": "string"}, {"metadata": {}, "name": "operationParameters", "nullable": true, "type": {"keyType": "string", "type": "map", "valueContainsNull": true, "valueType": "string"}}, {"metadata": {}, "name": "job", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "jobId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "job<PERSON>ame", "nullable": true, "type": "string"}, {"metadata": {}, "name": "jobRunId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "runId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "jobOwnerId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "triggerType", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "notebook", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "notebookId", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "clusterId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "readVersion", "nullable": true, "type": "long"}, {"metadata": {}, "name": "isolationLevel", "nullable": true, "type": "string"}, {"metadata": {}, "name": "isBlindAppend", "nullable": true, "type": "boolean"}, {"metadata": {}, "name": "operationMetrics", "nullable": true, "type": {"keyType": "string", "type": "map", "valueContainsNull": true, "valueType": "string"}}, {"metadata": {}, "name": "userMetadata", "nullable": true, "type": "string"}, {"metadata": {}, "name": "engineInfo", "nullable": true, "type": "string"}], "type": "struct"}}, "text/plain": ["<Spark SQL result set with 113 rows and 15 fields>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["%%sql\n", "DESCRIBE HISTORY authorizations\n"]}, {"cell_type": "code", "execution_count": null, "id": "467a5edb-0d0e-4063-b165-a5fa47ed3441", "metadata": {"microsoft": {"language": "python", "language_group": "synapse_pyspark"}}, "outputs": [], "source": []}], "metadata": {"dependencies": {"lakehouse": {"default_lakehouse": "a9dd9d49-c756-43b0-8bef-5318fe95a0cc", "default_lakehouse_name": "stage_cloud_data_lakehouse", "default_lakehouse_workspace_id": "4e510485-fa0c-403e-a7ce-4bb0d7292bec", "known_lakehouses": [{"id": "a9dd9d49-c756-43b0-8bef-5318fe95a0cc"}]}}, "kernel_info": {"name": "synapse_pyspark"}, "kernelspec": {"display_name": "synapse_pyspark", "name": "synapse_pyspark"}, "language_info": {"name": "python"}, "microsoft": {"language": "python", "language_group": "synapse_pyspark", "ms_spell_check": {"ms_spell_check_language": "en"}}, "nteract": {"version": "nteract-front-end@1.0.0"}, "spark_compute": {"compute_id": "/trident/default", "session_options": {"conf": {"spark.synapse.nbs.session.timeout": "1200000"}}}}, "nbformat": 4, "nbformat_minor": 5}