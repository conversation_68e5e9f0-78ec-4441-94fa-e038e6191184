locals {
  environment                                    = terraform.workspace
  fabric_capacity_display_name                   = "${local.environment}ffc"
  fabric_workspace_display_name                  = "${local.environment}_cloud_data_workspace"
  fabric_notebook_display_name                   = "${local.environment}_cloud_data_notebook"
  fabric_eventstream_display_name                = "${local.environment}_cloud_data_eventstream"
  fabric_lakehouse_display_name                  = "${local.environment}_cloud_data_lakehouse"
  fabric_workspace_managed_private_endpoint_name = "${local.environment}asdlsa"
  target_private_link_resource_id                = "/subscriptions/********-7f22-4468-a34d-d61125f01797/resourceGroups/data_lake_rg/providers/Microsoft.Storage/storageAccounts/stageasdlsa"
  admin_object_ids                               = [for id in split(",", var.fabric_workspace_admin_object_ids) : trimspace(id)]
}
