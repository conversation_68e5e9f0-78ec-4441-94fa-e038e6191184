terraform {
  required_version = ">= 1.4.6"

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.28"
    }

    azuread = {
      source  = "hashicorp/azuread"
      version = "~> 3.3"

    }
    fabric = {
      source  = "microsoft/fabric"
      version = "~> 1.6"
    }
  }

  backend "azurerm" {
  }
}

data "azurerm_client_config" "current" {}

provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
}

# Configure the Azure Active Directory Provider
provider "azuread" {
  tenant_id = data.azurerm_client_config.current.tenant_id
}

provider "fabric" {
  preview = "true"

}
