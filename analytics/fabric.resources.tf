data "fabric_capacity" "capacity" {
  display_name = local.fabric_capacity_display_name
}

resource "fabric_workspace" "workspace" {
  display_name = local.fabric_workspace_display_name
  capacity_id  = data.fabric_capacity.capacity.id
  identity = {
    type = "SystemAssigned"
  }
}

resource "fabric_workspace_role_assignment" "workspace_admins" {
  for_each = toset(local.admin_object_ids)

  workspace_id = fabric_workspace.workspace.id
  principal = {
    id   = each.value
    type = "User"
  }
  role = "Admin"
}

resource "fabric_notebook" "notebook" {
  workspace_id              = fabric_workspace.workspace.id
  display_name              = local.fabric_notebook_display_name
  definition_update_enabled = false
  format                    = "ipynb"
  definition = {
    "notebook-content.ipynb" = {
      source = "files/cloud_data_notebook.ipynb"
    }
  }
}

resource "fabric_spark_custom_pool" "spark_custom_pool" {
  workspace_id = fabric_workspace.workspace.id
  name         = "${local.environment}_cloud_data_spark_custom_pool"
  node_family  = "MemoryOptimized"
  node_size    = "Small"
  type         = "Workspace"

  auto_scale = {
    enabled        = true
    min_node_count = 1
    max_node_count = 3
  }

  dynamic_executor_allocation = {
    enabled       = true
    min_executors = 1
    max_executors = 2
  }
}
resource "fabric_lakehouse" "acquiring" {
  workspace_id = fabric_workspace.workspace.id
  display_name = local.fabric_lakehouse_display_name
  configuration = {
    enable_schemas = true
  }
}

# resource "fabric_eventstream" "acquiring" {
#   workspace_id = fabric_workspace.workspace.id
#   display_name = local.fabric_eventstream_display_name
# }

resource "fabric_workspace_managed_private_endpoint" "cloud_data_private_endpoint" {
  workspace_id                    = fabric_workspace.workspace.id
  name                            = local.fabric_workspace_managed_private_endpoint_name
  target_private_link_resource_id = local.target_private_link_resource_id
  target_subresource_type         = "dfs"
  request_message                 = "Fabric Managed Private Endpoint Access"
}
